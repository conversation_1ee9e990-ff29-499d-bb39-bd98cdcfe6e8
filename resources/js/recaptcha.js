// reCAPTCHA v3 implementation
class RecaptchaHandler {
    constructor() {
        this.siteKey = null;
        this.isLoaded = false;
        this.init();
    }

    async init() {
        try {
            // Get the site key from the meta tag (we'll add this to the layout)
            const metaTag = document.querySelector('meta[name="recaptcha-site-key"]');
            if (!metaTag) {
                console.error('reCAPTCHA site key not found in meta tag');
                return;
            }
            
            this.siteKey = metaTag.getAttribute('content');
            
            // Load reCAPTCHA script
            await this.loadRecaptchaScript();
            
            // Setup form handlers
            this.setupFormHandlers();
            
        } catch (error) {
            console.error('Failed to initialize reCAPTCHA:', error);
        }
    }

    loadRecaptchaScript() {
        return new Promise((resolve, reject) => {
            if (window.grecaptcha) {
                this.isLoaded = true;
                resolve();
                return;
            }

            const script = document.createElement('script');
            script.src = `https://www.google.com/recaptcha/api.js?render=${this.siteKey}`;
            script.async = true;
            script.defer = true;
            
            script.onload = () => {
                this.isLoaded = true;
                resolve();
            };
            
            script.onerror = () => {
                reject(new Error('Failed to load reCAPTCHA script'));
            };
            
            document.head.appendChild(script);
        });
    }

    async getToken(action = 'submit') {
        if (!this.isLoaded || !window.grecaptcha) {
            throw new Error('reCAPTCHA not loaded');
        }

        try {
            const token = await window.grecaptcha.execute(this.siteKey, { action });
            return token;
        } catch (error) {
            console.error('Failed to get reCAPTCHA token:', error);
            throw error;
        }
    }

    setupFormHandlers() {
        // Handle newsletter form
        const newsletterForm = document.querySelector('form[action="/!/forms/newsletter"]');
        if (newsletterForm) {
            this.setupNewsletterForm(newsletterForm);
        }
    }

    setupNewsletterForm(form) {
        const submitButton = form.querySelector('button[type="submit"]');
        const originalButtonText = submitButton ? submitButton.textContent : '';

        form.addEventListener('submit', async (e) => {
            e.preventDefault();
            
            if (submitButton) {
                submitButton.disabled = true;
                submitButton.textContent = 'Ověřuji...';
            }

            try {
                // Get reCAPTCHA token
                const token = await this.getToken('newsletter_submit');
                
                // Add token to form
                let tokenInput = form.querySelector('input[name="captcha"]');
                if (!tokenInput) {
                    tokenInput = document.createElement('input');
                    tokenInput.type = 'hidden';
                    tokenInput.name = 'captcha';
                    form.appendChild(tokenInput);
                }
                tokenInput.value = token;

                // Submit the form
                form.submit();
                
            } catch (error) {
                console.error('reCAPTCHA verification failed:', error);
                alert('Ověření se nezdařilo. Zkuste to prosím znovu.');
                
                if (submitButton) {
                    submitButton.disabled = false;
                    submitButton.textContent = originalButtonText;
                }
            }
        });
    }
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    new RecaptchaHandler();
});

// Export for potential external use
window.RecaptchaHandler = RecaptchaHandler;
