@extends('statamic::layout')
@section('title', $title)
@section('wrapper_class', 'max-w-full')

@section('content')
    <div class="flex items-center justify-between mb-6">
        <h1>{{ $title }}</h1>

    </div>

    <div class="card overflow-hidden p-0">
        <table data-size="sm" class="data-table">
            <tr>
                <th>Název</th>
                <td>{{ $company->getName() }}</td>
            </tr>
            <tr>
                <th>IČ</th>
                <td>{{ $company->getCin() }}</td>
            </tr>
            <tr>
                <th>DIČ</th>
                <td>{{ $company->getVat() }}</td>
            </tr>
            <tr>
                <th>Ulice a čp.</th>
                <td>{{ $company->getStreet() }}</td>
            </tr>
            <tr>
                <th>Město</th>
                <td>{{ $company->getCity() }}</td>
            </tr>
            <tr>
                <th>PSČ</th>
                <td>{{ $company->getZip() }}</td>
            </tr>
            <tr>
                <th>Registrováno</th>
                <td>{{ $company->getCreatedAt(true) }}</td>
            </tr>
            <tr>
                <th>Promo kód</th>
                <td>{{ $company->getPromoCode() }}</td>
            </tr>
            <tr>
                <th>Poznámka</th>
                <td>{!! $company->getNote(true) !!}</td>
            </tr>
        </table>
    </div>

    <h2>{{ __('Participants') }}</h2>

    <div class="card overflow-hidden p-0">
        <table data-size="sm" class="data-table">
            <tr>
                <th>Jméno</th>
                <th>Příjmení</th>
                <th>Email</th>
                <th>Telefon</th>
                <th>Prac. pozice</th>
                <th>Firma na jmenovku</th>
                <th>LinkedIn</th>
            </tr>
            @foreach ($company->getParticipants() as $participant)
                <tr>
                    <td>{{ $participant->getFirstName() }}</td>
                    <td>{{ $participant->getLastName() }}</td>
                    <td>{{ $participant->getEmail() }}</td>
                    <td>{{ $participant->getPhone() }}</td>
                    <td>{{ $participant->getJobPosition() }}</td>
                    <td>{{ $participant->getCompanyLabel() }}</td>
                    <td>
                        @if ($participant->getLinkedin())
                            <a href="{{ $participant->getLinkedin() }}" target="_blank">{{ $participant->getLinkedin() }}</a>
                        @endif
                    </td>
                </tr>
            @endforeach
        </table>
    </div>

@stop
