@extends('layout')

@section('content')
    <section class="bg-[#212121]" x-data="{ selectedDay: '{{ $days->first()->id }}' }">
        <div class="max-w-screen-xl px-4 py-8 mx-auto lg:px-6 sm:py-16 lg:py-24">
            <div class="max-w-3xl mx-auto text-center">
                <h1>{{ $title }}</h1>

                <div class="my-4">
                    @if ($day_select->raw())
                        <h3>{{ $day_select }}</h3>
                    @endif
                    <ul class="mt-6 flex items-center justify-center gap-6">
                        @foreach($days as $day)
                            <li>
                                <input
                                    type="radio"
                                    id="{{ $day->id }}"
                                    name="hosting"
                                    value="{{ $day->id }}"
                                    class="hidden peer"
                                    required
                                    x-model="selectedDay"
                                />
                                <label for="{{ $day->id }}" class="inline-flex px-3 py-2 text-white text-sm bg-white/5 border border-white/5 rounded-lg cursor-pointer peer-checked:border-primary peer-checked:text-white peer-checked:bg-primary hover:text-white hover:bg-primary">
                                    <div class="block">
                                        <div class="w-full text-lg font-semibold">{{ Statamic::modify($day->date)->format('j. n. Y') }}</div>
                                    </div>
                                </label>
                            </li>
                        @endforeach
                    </ul>
                </div>
            </div>

            @foreach($days as $day)
                <div
                    id="{{ $day->id }}"
                    class="flow-root max-w-3xl mx-auto mt-8 sm:mt-12 lg:mt-16"
                    x-show="selectedDay === '{{ $day->id }}'"
                    x-cloak
                >
                    <div class="-my-4 divide-y divide-gray-200 dark:divide-white/15">
                        @foreach($day->program as $set)
                            <div class="relative left-1/2 right-1/2 -ml-[50vw] -mr-[50vw] w-screen px-4" @if($set->bg_colour) style="background-color: {!! $set->bg_colour !!} @endif">
                                <div class="flex flex-col gap-2 py-4 sm:gap-6 sm:flex-row sm:items-center max-w-3xl mx-auto">
                                    <p class="w-32 text-lg font-normal @if($set->bg_colour) text-white @else text-gray-300 @endif sm:text-right shrink-0">
                                        {{ $set->from }} - {{ $set->to }}
                                    </p>
                                    <div class="flex flex-col">
                                        <h3 class="text-lg font-semibold text-white mb-0!">
                                            {{ $set->text }}
                                        </h3>
                                        @if ($set->description)
                                            <div class="text-gray-300">{{ $set->description }}</div>
                                        @endif
                                        @if ($set->speaker && $set->speaker->count())
                                            <span class="font-bold text-white mt-6 mb-2">Přednášející</span>
                                            @foreach ($set->speaker as $speaker)
                                                <a href="{{ $speaker->url() }}" class="group block shrink-0">
                                                    <div class="flex items-center">
                                                        <div>
                                                            <img class="inline-block size-12 rounded-full" src="{{ Statamic::tag('glide')->src($speaker->photo)->fit('crop_focal')->width(256)->height(256) }}" alt="" />
                                                        </div>
                                                        <div class="ml-3">
                                                            <p class="text-lg font-medium text-secondary group-hover:text-secondary-alt">{{ $speaker->title }}</p>
                                                            <p class="text-sm font-medium text-secondary-alt group-hover:text-secondary-light">{{ $speaker->position }}</p>
                                                        </div>
                                                    </div>
                                                </a>
                                            @endforeach
                                        @endif
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    </div>
                </div>
            @endforeach
        </div>
    </section>
@endsection
