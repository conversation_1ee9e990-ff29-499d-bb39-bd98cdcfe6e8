@extends('layout')

@section('content')
    <section class="antialiased">
        <div class="max-w-screen-xl px-4 py-8 mx-auto lg:px-6 sm:py-16 lg:py-24">
            <div class="max-w-3xl mx-auto text-center">
                <h2 class="text-secondary">
                    {{ $title }}
                </h2>

                @if ($perex->raw())
                    <p class="text-secondary-alt">{{ $perex }}</p>
                @endif
            </div>

            <ul role="list" class="mx-auto mt-20 grid max-w-2xl grid-cols-{{ $page->speakers_grid_mobile }} gap-x-8 gap-y-16 sm:grid-cols-{{ $page->speakers_grid_tablet }} lg:mx-0 lg:max-w-none lg:grid-cols-{{ $page->speakers_grid_desktop }}">
                @foreach($speakers as $speaker)
                    <li>
                        <a href="{{ $speaker->url() }}" class="group">
                            <img src="{{ Statamic::tag('glide')->src($speaker->photo)->fit('crop_focal')->width(1024)->height(1024) }}"
                                 class="aspect-3/2 w-full rounded-2xl object-cover transition duration-300 group-hover:scale-105 group-hover:shadow-lg"
                                 alt="{{ $speaker->alt ?? $speaker->title }}"
                            />
                            <h3 class="mt-6 text-lg/8 text-secondary">{{ $speaker->title }}</h3>
                            <p class="text-base/7 text-white">{{ $speaker->position }}</p>
                        </a>
                        <x-socials :speaker="$speaker" />
                    </li>
                @endforeach
            </ul>

        </div>
    </section>
@endsection
