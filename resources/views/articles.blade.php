@extends('layout')

@section('content')
    <h1>{{ $title }}</h1>

    <form method="get" action="{{ url()->current() }}">
        <s:taxonomy from="tags" collection="articles">
            <label><input type="checkbox" value="{{ $title }}" name="tags[]" @if(in_array($slug, request()->get('tags', []))) checked @endif>{{ $title }}</label>
        </s:taxonomy>
    </form>

    <s:collection :from="$mount">
        <div>
            <a href="{{ $url }}"><img src="{{ Statamic::tag('glide')->src($image)->width(400)->height(300)->fit('crop_focal')->fetch() }}"></a>
            <a href="{{ $url }}">{{ $title }}</a>
            <p>{{ $perex }}</p>
        </div>
    </s:collection>
@endsection
