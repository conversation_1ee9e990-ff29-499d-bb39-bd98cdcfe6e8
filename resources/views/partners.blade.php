@extends('layout')

@section('content')
    <div class="max-w-screen-xl px-4 py-8 mx-auto lg:px-6 sm:py-16 lg:py-24">
        <div class="max-w-7xl mx-auto text-center">
            <h1>{{ $title }}</h1>
            <h2>{{ $subtitle }}</h2>
        </div>

        <div class="flex justify-center">
            {!! $content !!}
        </div>
    </div>

    <div class="py-12 md:py-16 lg:py-20">
        <div class="mx-auto grid max-w-7xl grid-cols-1 gap-20 px-6 lg:px-8 xl:grid-cols-3">
            <s:collection:partners as="partners">
                @php $groupedEntries = $partners->groupBy(fn($entry) => $entry->partner_types->pluck('title')->toArray()) @endphp

                @foreach($groupedEntries as $group => $entries)
                    <div class="mx-auto max-w-2xl lg:mx-0">
                        <h2>{{ $group }}</h2>
                    </div>

                    <ul role="list" class="mx-auto grid max-w-2xl grid-cols-1 gap-x-6 gap-y-20 sm:grid-cols-2 lg:mx-0 lg:max-w-none lg:gap-x-8 xl:col-span-2">
                        @foreach($entries as $entry)
                            <li>
                                <a href="{{ $entry->web }}" target="_blank" class="block group">
                                    <div class="w-full h-32 rounded-2xl border border-white/5 bg-primary-alt p-3 flex items-center justify-center transition-transform duration-200 group-hover:scale-105 group-hover:shadow-lg">
                                        <img class="max-w-full max-h-full object-contain" src="{{ Statamic::tag('glide')->src($entry->logo)->height(160) }}" alt="{{ $entry->title }}" />
                                    </div>
                                    <h3 class="mt-6">{{ $entry->title }}</h3>
                                    {!! $entry->description !!}
                                </a>
                            </li>
                        @endforeach
                    </ul>
                @endforeach
            </s:collection:partners>
        </div>
    </div>

@endsection
