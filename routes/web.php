<?php

use App\Http\Controllers\RegistrationController;
use App\Http\Controllers\SpeakersController;
use Illuminate\Support\Facades\Route;

// Route::statamic('example', 'example-view', [
//    'title' => 'Example'
// ]);

Route::get('/prednasejici/{slug}', [SpeakersController::class, 'index'])->name('speakers.index');
Route::get('/{year}/registrace', [RegistrationController::class, 'index'])
    ->where('year', '[0-9]{4}')
    ->name('registration.index');
Route::post('/{year}/registrace', [RegistrationController::class, 'store'])
    ->where('year', '[0-9]{4}')
    ->name('registration.store');

Route::view('gdpr', 'gdpr')->name('gdpr');
