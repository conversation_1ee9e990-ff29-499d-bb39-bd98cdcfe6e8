---
id: b3b712bb-0939-46ff-9c90-2918f763c14b
origin: 88a75126-74ca-475a-9954-e4a39d4db387
title: 'Interesting Ideas from Last Year’s Edition'
perex: 'Keep Winning – How to Consistently Increase Project Success <PERSON><PERSON>, <PERSON><PERSON>, Chief of Staff to the CEO at the Project Management Institute, brought a global perspective and concrete data from PMI research. She discussed how to maintain the impact and success of projects in a constantly changing world:Start with what matters most – what does success'
updated_by: 2
updated_at: 1753714823
---
<h2>Keep Winning – How to Consistently Increase Project Success</h2><p><em><PERSON><PERSON>, PMI</em></p><p></p><p><PERSON><PERSON>, Chief of Staff to the CEO at the Project Management Institute, brought a global perspective and concrete data from PMI research. She discussed how to maintain the impact and success of projects in a constantly changing world:<br><br>Start with what matters most – what does success mean to you?</p><p>Only 37% of projects worldwide define success criteria at the beginning. Even fewer track progress effectively. Yet the combination of three steps – defining success, measuring performance, and monitoring progress – significantly increases the likelihood of project success.</p><p>Measurement pays off – but only if you measure what matters.</p><p>It’s not enough to track timelines and budgets. Real success is reflected in the project’s impact, the quality of deliverables, and stakeholder satisfaction.</p><p><br>Key takeaway?</p><p>Define, measure, and monitor. In today’s world, success isn’t measured by sticking to the budget – it’s measured by the impact the project creates. And that impact must be visible.</p><hr><h2>Winners and Losers of Agile Transformations</h2><p><em>Ondřej Kavula, Agisense</em></p><p></p><p>With humor and perspective, Ondřej shared his 20 years of experience from the Czech Republic and abroad, shedding light on what truly works – and what ruins agile transformations:</p><p>❌ <em>What doesn’t work:</em></p><ul><li><p>Big Bang approaches without clear goals</p></li><li><p>Blindly copying frameworks without considering company culture</p></li><li><p>Obsessive KPI tracking without understanding the value of teamwork</p></li><li><p>Underestimating technical excellence and the role of middle management</p></li></ul><p>✅ <em>What works:</em></p><ul><li><p>Aligning structure with culture (not the other way around)</p></li><li><p>Investing in middle management leadership – as coaches, teachers, and inspirers</p></li><li><p>Emphasizing technical skills and development culture – “Build the cult of software engineering”</p></li><li><p>Product-based transformations instead of structure-based ones</p></li></ul><p>„Agile won’t disappear – it’ll just be called something else. Adaptability is stronger than buzzwords.“</p><hr><h2>From Failure to Innovation</h2><p><em>Valér Dzvonik, ex-O2</em></p><p></p><p>Valér openly shared the story of a failed recruitment campaign at O2 that turned into an HR success – and most importantly, how the team managed to learn from it:</p><ul><li><p>The campaign’s failure, with minimal impact, prompted the team to rethink the entire recruitment strategy.</p></li><li><p>They focused on high school students and created a series of workshops (“How to Succeed in a Job Interview”), which have now reached over 300 young people.</p></li><li><p>Emphasis on authenticity, a healthy work environment, and clear communication paid off – new hire attrition dropped from 49% (2021) to 20% (2024).</p></li></ul><p>What began as a failure became an opportunity – the company is now building a long-term talent pool and has improved its employer reputation.</p>