<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Collection;

class Company extends AbstractModel
{
    protected $fillable = [
        'name',
        'street',
        'city',
        'zip',
        'cin',
        'vat',
        'note',
        'promo_code',
    ];

    private function participants(): HasMany
    {
        return $this->hasMany(Participant::class);
    }

    public function getParticipants(): Collection
    {
        return $this->participants()->get();
    }

    public function addParticipant(Participant $participant): self
    {
        $this->participants()->save($participant);
        return $this;
    }

    public function getYear(): int
    {
        return $this->attributes['year'];
    }

    public function setYear(int $year): self
    {
        $this->attributes['year'] = $year;
        return $this;
    }

    public function getName(): string
    {
        return $this->attributes['name'];
    }

    public function setName(string $name): self
    {
        $this->attributes['name'] = $name;
        return $this;
    }

    public function getStreet(): string
    {
        return $this->attributes['street'];
    }

    public function setStreet(string $street): self
    {
        $this->attributes['street'] = $street;
        return $this;
    }

    public function getCity(): string
    {
        return $this->attributes['city'];
    }

    public function setCity(string $city): self
    {
        $this->attributes['city'] = $city;
        return $this;
    }

    public function getZip(): string
    {
        return $this->attributes['zip'];
    }

    public function setZip(string|int $zip): self
    {
        $this->attributes['zip'] = (string) $zip;
        return $this;
    }

    public function getCin(): ?string
    {
        return $this->attributes['cin'];
    }

    public function setCin(string|int|null $cin): self
    {
        $this->attributes['cin'] = $cin !== null ? (string) $cin : null;
        return $this;
    }

    public function getVat(): ?string
    {
        return $this->attributes['vat'];
    }

    public function setVat(?string $vat): self
    {
        $this->attributes['vat'] = $vat;
        return $this;
    }

    public function getNote(bool $nl2br = false): ?string
    {
        $note = $this->attributes['note'];
        if ($note === null) {
            return null;
        }
        return $nl2br ? nl2br(htmlspecialchars($note)) : $note;
    }

    public function setNote(?string $note): self
    {
        $this->attributes['note'] = $note;
        return $this;
    }

    public function getPromoCode(): ?string
    {
        return $this->attributes['promo_code'];
    }

    public function setPromoCode(?string $promoCode): self
    {
        $this->attributes['promo_code'] = $promoCode;
        return $this;
    }
}
