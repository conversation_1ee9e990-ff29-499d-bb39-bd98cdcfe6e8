<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

abstract class AbstractModel extends Model
{
    public function getId(): int
    {
        return $this->attributes['id'];
    }

    public function getCreatedAt(string|true|null $format = null): \DateTimeImmutable|string
    {
        $createdAt = new \DateTimeImmutable($this->attributes['created_at']);
        $format = $format === true ? config('statamic.system.date_format') : $format;
        return $format ? $createdAt->format($format) : $createdAt;
    }

    public function getUpdatedAt(string|true|null $format = null): \DateTimeImmutable|string
    {
        $createdAt = new \DateTimeImmutable($this->attributes['updated_at']);
        $format = $format === true ? config('statamic.system.date_format') : $format;
        return $format ? $createdAt->format($format) : $createdAt;
    }
}
