<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Participant extends AbstractModel
{
    protected $fillable = [
        'company_id',
        'first_name',
        'last_name',
        'email',
        'phone',
        'job_position',
        'company_label',
        'linkedin',
    ];

    private function company(): BelongsTo
    {
        return $this->belongsTo(Company::class);
    }

    public function getCompany(): Company
    {
        return $this->company;
    }

    public function setCompany(Company $company): self
    {
        $this->company()->associate($company);
        return $this;
    }

    public function getFirstName(): string
    {
        return $this->attributes['first_name'];
    }

    public function setFirstName(string $firstName): self
    {
        $this->attributes['first_name'] = $firstName;
        return $this;
    }

    public function getLastName(): string
    {
        return $this->attributes['last_name'];
    }

    public function setLastName(string $lastName): self
    {
        $this->attributes['last_name'] = $lastName;
        return $this;
    }

    public function getEmail(): string
    {
        return $this->attributes['email'];
    }

    public function setEmail(string $email): self
    {
        $this->attributes['email'] = $email;
        return $this;
    }

    public function getPhone(): string
    {
        return $this->attributes['phone'];
    }

    public function setPhone(string $phone): self
    {
        $this->attributes['phone'] = $phone;
        return $this;
    }

    public function getJobPosition(): ?string
    {
        return $this->attributes['job_position'];
    }

    public function setJobPosition(?string $jobPosition): self
    {
        $this->attributes['job_position'] = $jobPosition;
        return $this;
    }

    public function getCompanyLabel(): ?string
    {
        return $this->attributes['company_label'];
    }

    public function setCompanyLabel(?string $companyLabel): self
    {
        $this->attributes['company_label'] = $companyLabel;
        return $this;
    }

    public function getLinkedin(): ?string
    {
        return $this->attributes['linkedin'];
    }

    public function setLinkedin(?string $linkedin): self
    {
        $this->attributes['linkedin'] = $linkedin;
        return $this;
    }
}
