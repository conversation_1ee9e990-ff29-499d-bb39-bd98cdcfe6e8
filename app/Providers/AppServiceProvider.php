<?php

namespace App\Providers;

use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\Vite;
use Illuminate\Support\ServiceProvider;
use Statamic\Facades\CP\Nav;
use Statamic\Statamic;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        // Forpsi MySQL rule
        Schema::defaultStringLength(191);

        // Vite override
        Vite::prefetch(concurrency: 3);

        Statamic::pushCpRoutes(function () {
            Route::namespace('\App\Http\Controllers\CP')->group(function () {
                require base_path('routes/cp.php');
            });
        });

        Nav::extend(function ($nav) {
            $nav->content(__('Registration'))
                ->route('registration.index')
                ->icon('users');
        });
    }
}
