<?php

namespace App\Listeners;

use App\Http\Requests\NewsletterFormRequest;
use App\Services\CaptchaService;
use Illuminate\Http\RedirectResponse;
use Statamic\Contracts\Forms\Submission;
use Statamic\Events\FormSubmitted;

readonly class NewsletterFormListener
{
    /**
     * Handle the event.
     */
    public function handle(FormSubmitted $event): void
    {
        if ($event->submission->form->handle() === 'newsletter') {
            $request = app(NewsletterFormRequest::class);
            $request->validateResolved();

            $this->newsletterFormSubmit($event->submission);
        }
    }

    private function newsletterFormSubmit(Submission $submission): void
    {
        // Captcha verification
        if (! CaptchaService::checkScore(request('captcha'))) {
            // Redirect with error
            redirect()
                ->back()
                ->withFragment('#newsletter')
                ->withInput()
                ->withErrors(['captcha' => 'Ověření reCAPTCHA se nezdařilo. Zkuste to prosím znovu.'], 'newsletter')
                ->send();
            return;
        }

        // If we get here, the form submission was successful
        // You can add your newsletter subscription logic here

        // Redirect with success message
        redirect()
            ->back()
            ->withFragment('#newsletter')
            ->with('newsletter_success', 'Děkujeme! Váš e-mail byl úspěšně přidán do našeho newsletteru.')
            ->send();
    }
}
