<?php

namespace App\Livewire;

use Illuminate\View\View;
use Livewire\Component;

class RegistrationForm extends Component
{
    public int $registrationStep = 1;
    public int $totalSteps = 4;
    public int $members = 1;

    public string $title;
    public ?string $ticketType = null;
    public bool $showGdpr = false;

    public function mount(string $title): void
    {
        $this->title = $title;
    }

    public function setStep(int $step): void
    {
        $this->registrationStep = $step;
    }

    public function setTicketType($value): void
    {
        $this->ticketType = $value;
        $this->registrationStep = 2;
    }

    public function addMember(): void
    {
        $this->members++;
    }

    public function removeMember(): void
    {
        if ($this->members > 1) {
            $this->members--;
        }
    }

    public function toggleGdpr(): void
    {
        $this->showGdpr = !$this->showGdpr;
    }

    public function render(): View
    {
        return view('livewire.registration-form');
    }
}
