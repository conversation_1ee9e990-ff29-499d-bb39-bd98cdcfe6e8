<?php

namespace App\Http\Controllers;

use Statamic\Auth\Protect\Protection;
use Statamic\Entries\Entry;
use Statamic\Exceptions\NotFoundHttpException;
use Statamic\Facades;
use Statamic\Sites\Site;
use Statamic\Taxonomies\LocalizedTerm;
use Statamic\View\View;

class SpeakersController extends Controller
{
    private Site $site;

    public function __construct()
    {
        $this->site = Facades\Site::current();
    }

    public function index(string $slug): View
    {
        $speaker = $this->fetchSpeaker($slug);
        if (!$speaker) {
            $group = $this->fetchGroup($slug);
        }

        if ($speaker) {
            return $this->speakerView($speaker);
        } elseif ($group) {
            return $this->groupView($group);
        } else {
            // See \Statamic\Http\Controllers\FrontendController::index
            app(Protection::class)->protect();
            throw new NotFoundHttpException;
        }
    }

    private function fetchSpeaker(string $slug): ?Entry
    {
        return Facades\Entry::query()
            ->where('slug', $slug)
            ->where('published', true)
            ->where('locale', $this->site->handle())
            ->first();
    }

    private function fetchGroup(string $slug): ?LocalizedTerm
    {
        return Facades\Term::query()
            ->where('slug', $slug)
            ->where('published', true)
            ->where('locale', $this->site->handle())
            ->first();
    }

    private function speakerView(Entry $speaker): View
    {
        $groups = $speaker->groups()
            ->where('published', true)
            ->where('locale', $this->site->handle())
            ->get();
        return View::make('speakers.index', [
            'title' => $speaker->title,
            'speaker' => $speaker,
            'groups' => $groups
        ]);
    }

    private function groupView(LocalizedTerm $group): View
    {
        $speakers = $group->entries()
            ->where('published', true)
            ->where('locale', $this->site->handle());
        return View::make('speakers.group', [
            'title' => $group->title,
            'speakers' => $speakers,
            'group' => $group
        ]);
    }
}
