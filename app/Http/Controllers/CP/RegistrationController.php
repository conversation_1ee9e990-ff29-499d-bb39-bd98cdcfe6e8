<?php

namespace App\Http\Controllers\CP;

use App\Http\Controllers\Controller;
use App\Models\Company;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Statamic\Auth\Protect\Protection;
use Statamic\Entries\Entry;
use Statamic\Exceptions\NotFoundHttpException;
use Statamic\Facades;
use Statamic\Http\Controllers\FrontendController;
use Statamic\Sites\Site;
use Statamic\Taxonomies\LocalizedTerm;
use Statamic\View\View;

class RegistrationController extends Controller
{
    public function index(Request $request)
    {
        $companies = Company::query()
            ->where('year', '=', 2025)
            ->orderByDesc('created_at')
            ->limit(50)
            ->offset(0)
            ->get();
        return view('cp.registration.index', [
            'title' => __('Registration'),
            'companies' => $companies
        ]);
    }

    public function show(Request $request, Company $company)
    {
        return view('cp.registration.show', [
            'title' => __('Detail of registration'),
            'company' => $company
        ]);
    }
}
