<?php

namespace App\Http\Controllers;

use App\Models\Company;
use App\Models\Participant;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Statamic\Auth\Protect\Protection;
use Statamic\Entries\Entry;
use Statamic\Exceptions\NotFoundHttpException;
use Statamic\Facades;
use Statamic\Http\Controllers\FrontendController;
use Statamic\Sites\Site;
use Statamic\Taxonomies\LocalizedTerm;
use Statamic\View\View;

class RegistrationController extends FrontendController
{
    private Site $site;

    public function __construct()
    {
        $this->site = Facades\Site::current();
    }

    public function index(Request $request)
    {
        return parent::index($request);
    }

    public function store(Request $request, string $year)
    {
        $this->fakeData((int) $year);
        $validated = $request->validate([
            'first_name' => 'required',
            'last_name' => 'required',
            'email' => 'required|email',
            'phone' => 'required',
            'name' => 'required',
            'street' => 'required',
            'city' => 'required',
            'zip' => 'required,'
        ]);

        DB::beginTransaction();
        $participant = Participant::create($validated);
        $company = Company::create($validated);
        $company->setYear((int) $year);
        $company->save();
        $participant->setCompany($company);
        $participant->save();
        DB::commit();

    }

    private function fakeData(int $year)
    {
        $faker = \Faker\Factory::create();

        DB::beginTransaction();

        $company = new Company();
        $company->setYear($year)
            ->setName($faker->company())
            ->setStreet($faker->streetAddress())
            ->setCity($faker->city())
            ->setZip($faker->postcode());
        $company->save();

        $random = rand(0, 9);
        for ($i = 0; $i <= $random; $i++) {
            $participant = new Participant();
            $participant->setFirstName($faker->name())
                ->setLastName($faker->lastName())
                ->setEmail($faker->email())
                ->setPhone($faker->phoneNumber())
                ->setJobPosition($faker->word())
                ->setLinkedin($faker->url());
            $participant->setCompany($company);
            $participant->save();
        }

        DB::commit();
    }
}
