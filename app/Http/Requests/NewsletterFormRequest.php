<?php

namespace App\Http\Requests;

use Illuminate\Contracts\Validation\ValidationRule;
use Illuminate\Foundation\Http\FormRequest;

class NewsletterFormRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'email' => 'required|email',
        ];
    }

    public function messages(): array
    {
        return [
            'email.required' => 'Email je povinný',
            'email.email' => 'Email musí být platná adresa',
        ];
    }

    protected function getRedirectUrl(): string
    {
        return back()
            ->withFragment('#newsletter')
            ->withInput()
            ->withErrors($this->validator, 'newsletter')
            ->getTargetUrl();
    }
}
