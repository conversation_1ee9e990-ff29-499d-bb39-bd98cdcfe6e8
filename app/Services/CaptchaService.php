<?php

namespace App\Services;

class CaptchaService
{
    public static function checkScore(?string $captcha = null): bool
    {
        if (empty($captcha)) {
            return false;
        }

        $url = 'https://www.google.com/recaptcha/api/siteverify';
        $threshold = config('recaptcha.threshold');
        $data = [
            'secret' => config('recaptcha.secret_key'),
            'response' => $captcha,
            'remoteip' => request()->ip(),
        ];

        $options = [
            'http' => [
                'header' => "Content-type: application/x-www-form-urlencoded\r\n",
                'method' => 'POST',
                'content' => http_build_query($data),
            ],
        ];

        $context = stream_context_create($options);
        $result = file_get_contents($url, false, $context);
        $resultJson = json_decode($result);

        return $resultJson->success && $resultJson->score >= $threshold;
    }
}
