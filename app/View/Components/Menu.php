<?php

namespace App\View\Components;

use Closure;
use Illuminate\Contracts\View\View;
use Illuminate\View\Component;
use Statamic\Contracts\Structures\Tree;
use Statamic\Entries\Entry;
use Statamic\Facades;
use Statamic\Sites\Site;

class Menu extends Component
{
    private string $year;
    private Site $site;
    private string $currentUrl;

    public function __construct()
    {
        $this->year = $this->getYear();
        $this->site = Facades\Site::current();
        $this->currentUrl = '/' . trim(request()->uri()->path(), '/');
    }

    private function getYear(): string
    {
        if (preg_match('#/([0-9]{4})/#', request()->url(), $m)) {
            return $m[1];
        } else {
            return Facades\GlobalSet::findByHandle('base')
                ->inCurrentSite()
                ->current_year ?? date('Y');
        }
    }

    public function render(): View|Closure|string
    {
        return view('components.menu', [
            'nav' => $this->getNav(),
            'sites' => $this->getSites()
        ]);
    }

    private function getSites(): array
    {
        $result = [];
        foreach (Facades\Site::all() as $site) {
            $result[] = $this->siteToItem($site);
        }
        return $result;
    }

    private function getNav(): array
    {
        $nav = $this->getStructure()->tree();
        $result = [];
        foreach ($nav as $item) {
            $entry = $this->getEntry($item['entry']);
            if ($entry->url() === '/' || $entry->url() === '/en') { /* TODO vylepšit */
                continue;
            }
            if ($entry->blueprint->handle === 'year') {
                if ($entry->slug === $this->year) {
                    foreach ($item['children'] as $child) {
                        $entry = $this->getEntry($child['entry']);
                        $result[] = $this->entryToItem($entry);
                    }
                }
            } else {
                $result[] = $this->entryToItem($entry);
            }
        }
        return $result;
    }

    private function getStructure(): Tree
    {
        return Facades\Collection::findByHandle('pages')
            ->structure()
            ->trees()
            ->get($this->site->handle());
    }

    private function getEntry(string $id): Entry
    {
        $query = Facades\Entry::query()
            ->where('published', true)
            ->where('id', $id);

        return $query->first();
    }

    private function entryToItem(Entry $entry): array
    {
        return [
            'title' => $entry->title,
            'url' => $entry->url,
            'active' => $this->currentUrl === $entry->url
        ];
    }

    private function siteToItem(Site $site): array
    {
        return [
            'title' => strtoupper($site->handle),
            'url' => $site->url,
            'active' => $this->site->handle === $site->handle,
        ];
    }
}
