<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Google Recaptcha config
    |--------------------------------------------------------------------------
    |
    | In this file, there is a setting of what needs to be set for Google
    | reCaptcha. This website actually uses v3, which is score-based. Required
    | threshold needs to be set to recognize bots from real humans.
    |
    */

    'threshold' => env('RECAPTCHA_THRESHOLD', 0.5),

    /*
    |--------------------------------------------------------------------------
    | reCaptcha site key
    |--------------------------------------------------------------------------
    |
    | The reCaptcha site key is a key that set up the recaptcha token in all forms.
    | The key should be stored in .env file in project root.
    |
    */

    'site_key' => env('RECAPTCHA_SITE_KEY'),

    /*
    |--------------------------------------------------------------------------
    | reCaptcha secret key
    |--------------------------------------------------------------------------
    |
    | The reCaptcha secret key must be hidden at all times and inaccessible to
    | anyone from outside. The key should be stored in .env file in project root.
    |
    */

    'secret_key' => env('RECAPTCHA_SECRET_KEY'),

];
