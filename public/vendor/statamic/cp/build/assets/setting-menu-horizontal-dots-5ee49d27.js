var t=function(){var r=this,e=r._self._c;return e("svg",{attrs:{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 48 48"}},[e("path",{attrs:{fill:"currentColor","fill-rule":"evenodd",d:"M2.044 8.136C1.778 11.407 1.5 16.599 1.5 24c0 7.401.278 12.593.544 15.864.267 3.288 2.804 5.825 6.092 6.092 3.271.266 8.463.544 15.864.544 7.401 0 12.593-.278 15.864-.544 3.288-.267 5.825-2.804 6.092-6.092.266-3.271.544-8.463.544-15.864 0-7.401-.278-12.593-.544-15.864-.267-3.288-2.804-5.825-6.092-6.092C36.593 1.778 31.402 1.5 24 1.5c-7.401 0-12.593.278-15.864.544-3.288.267-5.825 2.804-6.092 6.092ZM24 27.5a3.5 3.5 0 1 0 0-7 3.5 3.5 0 0 0 0 7ZM16 24a3.5 3.5 0 1 1-7 0 3.5 3.5 0 0 1 7 0Zm19.5 3.5a3.5 3.5 0 1 0 0-7 3.5 3.5 0 0 0 0 7Z","clip-rule":"evenodd"}})])};const l={render:t};export{l as default};
