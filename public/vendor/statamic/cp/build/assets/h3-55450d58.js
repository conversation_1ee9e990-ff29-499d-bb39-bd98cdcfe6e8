var e=function(){var r=this,t=r._self._c;return t("svg",{attrs:{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 14 14"}},[t("defs",[t("clipPath",{attrs:{id:"a"}},[t("path",{attrs:{fill:"none",d:"M0 0h14v14H0z"}})])]),t("g",{attrs:{"clip-path":"url(#a)"}},[t("path",{attrs:{fill:"none",stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"1.5",d:"M9.54 11.87c.*********** 1.25.88h.88c.73 0 1.33-.59 1.33-1.33v-.22c0-.73-.59-1.33-1.33-1.33h-.44.33c.67 0 1.22-.54 1.22-1.22s-.54-1.22-1.22-1.22h-.66c-.56 0-1.03.37-1.17.88M1 12.75V1.25m5.75 0v11.5M1 6.52h5.75"}})])])};const s={render:e};export{s as default};
