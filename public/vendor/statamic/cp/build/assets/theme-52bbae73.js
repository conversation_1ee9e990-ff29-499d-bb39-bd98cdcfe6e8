var e=function(){var t=this,r=t._self._c;return r("svg",{attrs:{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 16 16"}},[r("path",{attrs:{fill:"none",stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round","stroke-miterlimit":"10",d:"M5.5 15.5h5m-2.5-2v2M.5 11h15m-1-9c.6 0 1 .4 1 1v9.5c0 .6-.4 1-1 1h-13c-.6 0-1-.4-1-1V3c0-.6.4-1 1-1"}}),r("path",{attrs:{fill:"none",stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round","stroke-miterlimit":"10",d:"M3.5 9V1.5c0-.6.4-1 1-1h7c.6 0 1 .4 1 1V9"}}),r("path",{attrs:{fill:"none",stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round","stroke-miterlimit":"10",d:"M5.5 4.5h2v2h-2zm-2-2h9m-3 2h1M9.5 6h1m-5 2.5h5"}})])};const n={render:e};export{n as default};
