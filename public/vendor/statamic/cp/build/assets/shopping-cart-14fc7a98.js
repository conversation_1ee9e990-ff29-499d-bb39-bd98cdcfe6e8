var e=function(){var l=this,r=l._self._c;return r("svg",{attrs:{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 48 48"}},[r("path",{attrs:{fill:"currentColor","fill-rule":"evenodd",d:"M38.53 1.5h4.659c1.7 0 3.225 1.242 3.297 3.015a17.134 17.134 0 0 1 0 1.37C46.413 7.658 44.889 8.9 43.189 8.9h-3.097c-.811 2.856-1.536 5.58-2.25 8.267-1.14 4.284-2.254 8.475-3.656 12.962-.637 2.04-2.295 3.605-4.464 3.87-2.085.254-5.296.501-9.913.501-4.745 0-7.899-.26-9.872-.523-1.937-.258-3.445-1.61-4.077-3.421-1.492-4.277-2.817-9.513-3.684-14.41C1.701 13.46 3.82 11.1 6.51 11.1h24.98l1.79-5.739A5.5 5.5 0 0 1 38.53 1.5ZM13.718 17.02a2 2 0 0 1 2.263 1.697l1 7a2 2 0 0 1-3.96.566l-1-7a2 2 0 0 1 1.697-2.263Zm10.303 1.697a2 2 0 0 1 3.96.566l-1 7a2 2 0 0 1-3.96-.566l1-7Z","clip-rule":"evenodd"}}),r("path",{attrs:{fill:"currentColor",d:"M5.5 41.5a5 5 0 1 1 10 0 5 5 0 0 1-10 0ZM29.5 36.5a5 5 0 1 0 0 10 5 5 0 0 0 0-10Z"}})])};const a={render:e};export{a as default};
