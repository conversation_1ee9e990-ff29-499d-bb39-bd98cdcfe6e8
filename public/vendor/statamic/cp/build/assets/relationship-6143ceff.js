var e=function(){var r=this,t=r._self._c;return t("svg",{attrs:{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"}},[t("g",{attrs:{fill:"none",stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round"}},[t("path",{attrs:{"stroke-width":"1.5",d:"M17.2 9.8h4.5c.8 0 1.5-.7 1.5-1.5V3.6c0-.4-.2-.8-.4-1.1l-1.4-1.4c-.3-.3-.7-.4-1.1-.4h-3.1c-.8 0-1.5.7-1.5 1.5v6c.1.9.7 1.6 1.5 1.6z"}}),t("path",{attrs:{"stroke-width":"1.5",d:"M18.8.8v3c0 .8.7 1.5 1.5 1.5h3"}}),t("path",{attrs:{"stroke-width":"1.775",d:"M2.5 23.2h5.4c1 0 1.8-.8 1.8-1.8V16c0-.5-.2-.9-.5-1.2l-1.6-1.6c-.3-.3-.8-.5-1.3-.5H2.5c-1 0-1.8.8-1.8 1.8v7c0 .9.8 1.7 1.8 1.7z"}}),t("path",{attrs:{"stroke-width":"1.667",d:"M4.2 13.2v3.3c0 .9.7 1.7 1.7 1.7h3.3"}}),t("path",{attrs:{"stroke-width":"1.5",d:"M12.8 3.8h-7c-.8 0-1.5.7-1.5 1.5v4.5"}}),t("path",{attrs:{"stroke-width":"1.5",d:"m2 7.5 2.2 2.2 2.2-2.2M10.9 6l2.2-2.2-2.2-2.2"}})])])};const h={render:e};export{h as default};
