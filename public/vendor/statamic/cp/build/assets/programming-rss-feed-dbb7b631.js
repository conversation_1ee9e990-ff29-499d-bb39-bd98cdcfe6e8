var s=function(){var r=this,e=r._self._c;return e("svg",{attrs:{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 48 48"}},[e("path",{attrs:{fill:"currentColor","fill-rule":"evenodd",d:"M7.18 46.913c3.412.284 8.91.587 16.82.587s13.408-.303 16.82-.587c3.285-.272 5.82-2.808 6.093-6.093.284-3.412.587-8.91.587-16.82s-.303-13.408-.587-16.82c-.272-3.285-2.808-5.82-6.093-6.094C37.408.803 31.91.5 24 .5s-13.408.303-16.82.586C3.895 1.36 1.36 3.895 1.086 7.18.803 10.592.5 16.09.5 24s.303 13.408.586 16.82c.273 3.285 2.809 5.82 6.094 6.093ZM10.5 10.5a3 3 0 0 1 3-3c14.912 0 27 12.088 27 27a3 3 0 1 1-6 0c0-11.598-9.402-21-21-21a3 3 0 0 1-3-3Zm0 12a3 3 0 0 1 3-3c8.284 0 15 6.716 15 15a3 3 0 1 1-6 0 9 9 0 0 0-9-9 3 3 0 0 1-3-3Zm-1 12a4 4 0 1 1 8 0 4 4 0 0 1-8 0Z","clip-rule":"evenodd"}})])};const n={render:s};export{n as default};
