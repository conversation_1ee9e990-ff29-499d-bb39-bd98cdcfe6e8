var t=function(){var r=this,e=r._self._c;return e("svg",{attrs:{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 48 48"}},[e("path",{attrs:{fill:"currentColor","fill-rule":"evenodd",d:"M23.87 2.288c.717-.641 1.744-.978 2.838-.674.844.235 1.664.58 2.443 1.035 3.157 1.847 7.464 4.824 10.996 8.954 3.54 4.14 6.355 9.506 6.355 16.074 0 6.426-2.553 11.215-6.754 14.35-4.142 3.09-9.745 4.475-15.748 4.475s-11.606-1.385-15.748-4.475c-4.201-3.136-6.754-7.924-6.754-14.35 0-9.598 5.995-16.61 11.543-21.051 2.073-1.66 4.786-.36 5.532 1.736.513 1.438 1.123 2.583 1.79 *************.112.076.21.063a.558.558 0 0 0 .36-.23c1.143-1.554 1.645-4.105 1.78-6.684.049-.946.464-1.848 1.157-2.467Zm.857 21.044a1.64 1.64 0 0 0-1.454 0C21.302 24.3 16 27.39 16 32.6c0 4.418 3.582 6.4 8 6.4s8-1.982 8-6.4c0-5.21-5.302-8.3-7.273-9.268Z","clip-rule":"evenodd"}})])};const n={render:t};export{n as default};
