var t=function(){var e=this,r=e._self._c;return r("svg",{attrs:{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 48 48"}},[r("path",{attrs:{fill:"currentColor",d:"M24 16a8 8 0 1 0 0 16 8 8 0 0 0 0-16Z"}}),r("path",{attrs:{fill:"currentColor","fill-rule":"evenodd",d:"M24 46.5c-4.722 0-8.545-.113-11.543-.264-5.89-.297-10.396-4.803-10.693-10.693C1.614 32.545 1.5 28.723 1.5 24c0-4.722.113-8.545.264-11.543.297-5.89 4.803-10.396 10.693-10.693C15.455 1.614 19.277 1.5 24 1.5c4.722 0 8.545.113 11.543.264 5.89.297 10.396 4.803 10.693 10.693.15 2.998.264 6.82.264 11.543 0 4.722-.113 8.545-.264 11.543-.297 5.89-4.803 10.396-10.693 10.693-2.998.15-6.82.264-11.543.264ZM12 24c0-6.627 5.373-12 12-12s12 5.373 12 12-5.373 12-12 12-12-5.373-12-12ZM37 8a2 2 0 1 0 0 4h1a2 2 0 1 0 0-4h-1Z","clip-rule":"evenodd"}})])};const a={render:t};export{a as default};
