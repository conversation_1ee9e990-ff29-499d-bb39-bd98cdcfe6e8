var e=function(){var r=this,t=r._self._c;return t("svg",{attrs:{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 14 14"}},[t("defs",[t("clipPath",{attrs:{id:"a"}},[t("path",{attrs:{fill:"none",d:"M0 0h14v14H0z"}})])]),t("g",{attrs:{"clip-path":"url(#a)"}},[t("path",{attrs:{fill:"none",stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"1.5",d:"M12.93 12.75H9.61V12c0-.53.29-1 .74-1.22l1.84-.86c.44-.21.73-.67.73-1.18 0-.71-.54-1.29-1.21-1.29h-.86c-.54 0-1 .37-1.17.88M1 12.75V1.25m5.75 0v11.5M1 6.52h5.75"}})])])};const s={render:e};export{s as default};
