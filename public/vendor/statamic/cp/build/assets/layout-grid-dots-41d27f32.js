var a=function(){var e=this,r=e._self._c;return r("svg",{attrs:{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 48 48"}},[r("path",{attrs:{fill:"currentColor","fill-rule":"evenodd",d:"M8.136 2.044C11.407 1.778 16.599 1.5 24 1.5c7.401 0 12.593.278 15.864.544 3.288.267 5.825 2.804 6.092 6.092.266 3.271.544 8.463.544 15.864 0 7.401-.278 12.593-.544 15.864-.267 3.288-2.804 5.825-6.092 6.092-3.271.266-8.463.544-15.864.544-7.401 0-12.593-.278-15.864-.544-3.288-.267-5.825-2.804-6.092-6.092C1.778 36.593 1.5 31.401 1.5 24c0-7.401.278-12.593.544-15.864.267-3.288 2.804-5.825 6.092-6.092ZM10.5 13a2.5 2.5 0 1 1 5 0 2.5 2.5 0 0 1-5 0Zm11 0a2.5 2.5 0 1 1 5 0 2.5 2.5 0 0 1-5 0Zm11 0a2.5 2.5 0 1 1 5 0 2.5 2.5 0 0 1-5 0Zm-22 11a2.5 2.5 0 1 1 5 0 2.5 2.5 0 0 1-5 0Zm11 0a2.5 2.5 0 1 1 5 0 2.5 2.5 0 0 1-5 0Zm11 0a2.5 2.5 0 1 1 5 0 2.5 2.5 0 0 1-5 0Zm-22 11a2.5 2.5 0 1 1 5 0 2.5 2.5 0 0 1-5 0Zm11 0a2.5 2.5 0 1 1 5 0 2.5 2.5 0 0 1-5 0Zm11 0a2.5 2.5 0 1 1 5 0 2.5 2.5 0 0 1-5 0Z","clip-rule":"evenodd"}})])};const l={render:a};export{l as default};
