var t=function(){var e=this,r=e._self._c;return r("svg",{attrs:{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"}},[r("path",{attrs:{fill:"none",stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round",d:"M11 18.5H1.5a1 1 0 0 1-1-1v-16a1 1 0 0 1 1-1h15a1 1 0 0 1 1 1v9.965"}}),r("rect",{attrs:{width:"13",height:"5",x:"2.5",y:"2.5",fill:"none",stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round",rx:".5",ry:".5"}}),r("path",{attrs:{fill:"none",stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round",d:"M2.5 10v6a.5.5 0 0 0 .5.5h6.337a.5.5 0 0 0 .481-.637l-1.714-6a.5.5 0 0 0-.481-.363H3a.5.5 0 0 0-.5.5zm13 3.5V10a.5.5 0 0 0-.5-.5h-3.837a.5.5 0 0 0-.481.637L12.5 16.5h.458m2.742 5.8-4.2 1.2 1.2-4.2 7.179-7.179a2.121 2.121 0 0 1 3 3zm3.279-9.279 3 3M12.7 19.3l3 3"}})])};const o={render:t};export{o as default};
