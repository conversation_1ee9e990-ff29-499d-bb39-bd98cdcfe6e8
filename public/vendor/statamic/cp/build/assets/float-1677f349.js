var t=function(){var o=this,r=o._self._c;return r("svg",{attrs:{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 15 15.9"}},[r("path",{attrs:{fill:"none",stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round","stroke-width":".75",d:"M5.1 11V5.5c0-.2-.1-.4-.3-.4-.1-.1-.3-.1-.5 0L3.1 6m1 5h2"}}),r("circle",{attrs:{cx:"10",cy:"6.5",r:"1.5",fill:"none",stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round","stroke-width":".75"}}),r("path",{attrs:{fill:"none",stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round","stroke-width":".75",d:"M9.5 11c1.1 0 2-.9 2-2V6.5M7.8 11h0"}}),r("path",{attrs:{fill:"none",stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round","stroke-width":".75",d:"M.5.5h14v14.9H.5V.5z"}})])};const n={render:t};export{n as default};
