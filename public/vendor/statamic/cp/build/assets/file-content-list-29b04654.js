var t=function(){var e=this,r=e._self._c;return r("svg",{attrs:{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 48 48"}},[r("path",{attrs:{fill:"currentColor","fill-rule":"evenodd",d:"M44.432 18.298c.043 1.716.068 3.612.068 5.702 0 8.11-.377 13.307-.727 16.384-.329 2.899-2.531 5.088-5.42 5.425-2.878.336-7.535.691-14.353.691-6.819 0-11.475-.355-14.354-.691-2.888-.337-5.09-2.526-5.42-5.425C3.876 37.307 3.5 32.11 3.5 24c0-8.11.377-13.307.726-16.384.33-2.899 2.532-5.088 5.42-5.425C12.526 1.855 17.181 1.5 24 1.5c1.31 0 2.538.013 3.69.037-.002.741-.002 1.691.007 2.73.02 2.363.083 5.25.267 7.164.334 3.483 3.028 6.246 6.493 6.579 1.913.183 4.822.252 7.208.276 1.053.01 2.016.013 2.767.012ZM20 35a2 2 0 0 0 2 2h11a2 2 0 1 0 0-4H22a2 2 0 0 0-2 2Zm2-7a2 2 0 1 1 0-4h7a2 2 0 1 1 0 4h-7Zm-9 7a2 2 0 0 0 2 2h1a2 2 0 1 0 0-4h-1a2 2 0 0 0-2 2Zm2-7a2 2 0 1 1 0-4h1a2 2 0 1 1 0 4h-1Z","clip-rule":"evenodd"}}),r("path",{attrs:{fill:"currentColor",d:"M30.95 11.145c-.17-1.768-.233-4.534-.253-6.903-.007-.836-.008-1.614-.007-2.27 1.507.635 4.184 2.121 7.662 5.513 3.75 3.657 5.233 6.41 5.8 7.813-.696 0-1.542-.003-2.456-.012-2.393-.024-5.183-.093-6.952-.263-1.994-.191-3.593-1.784-3.794-3.878Z"}})])};const l={render:t};export{l as default};
