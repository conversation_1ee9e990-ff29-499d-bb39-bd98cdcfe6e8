var a=function(){var t=this,r=t._self._c;return r("svg",{attrs:{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"}},[r("g",{attrs:{fill:"none",stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round"}},[r("path",{attrs:{d:"m4.238 14.416-3.171 5.475a.5.5 0 0 0 .516.744l2.8-.474.975 2.679a.5.5 0 0 0 .9.085l2.723-4.607m10.781-3.902 3.171 5.475a.5.5 0 0 1-.516.744l-2.8-.474-.975 2.679a.5.5 0 0 1-.9.085l-2.723-4.607"}}),r("path",{attrs:{d:"M2.984 9.83a9 9 0 1 0 18 0 9 9 0 1 0-18 0Z"}}),r("path",{attrs:{d:"m12.572 5.189 1.282 2.644h2.5a.613.613 0 0 1 .427 1.067l-2.166 2.135 1.2 2.761a.654.654 0 0 1-.931.818l-2.9-1.634-2.9 1.634a.654.654 0 0 1-.931-.818l1.2-2.761L7.187 8.9a.612.612 0 0 1 .429-1.069h2.5L11.4 5.189a.661.661 0 0 1 1.172 0Z"}})])])};const n={render:a};export{n as default};
