var l=function(){var r=this,e=r._self._c;return e("svg",{attrs:{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 14 14"}},[e("path",{attrs:{fill:"currentColor","fill-rule":"evenodd",d:"M3.381.62A49.93 49.93 0 0 1 7 .5c.452 0 .875.004 1.28.013.504.011.998.178 1.403.49 1.196.924 1.928 1.704 2.832 2.935.293.399.451.877.464 1.366a67.276 67.276 0 0 1-.106 5.811 2.423 2.423 0 0 1-2.254 2.265c-1.145.079-2.25.12-3.619.12-1.368 0-2.474-.041-3.619-.12a2.423 2.423 0 0 1-2.254-2.265A65.804 65.804 0 0 1 1 7c0-1.41.044-2.786.127-4.115A2.423 2.423 0 0 1 3.381.62Zm2.166 7.545-.868-.869a.5.5 0 0 0-.852.305c-.107 1.077-.107 1.686 0 2.721a.5.5 0 0 0 .446.446c1.036.107 1.644.107 2.722 0a.5.5 0 0 0 .304-.851l-.868-.868 1.793-1.793a.625.625 0 1 0-.884-.884L5.547 8.165Z","clip-rule":"evenodd"}})])};const a={render:l};export{a as default};
