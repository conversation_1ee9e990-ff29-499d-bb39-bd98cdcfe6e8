var e=function(){var r=this,t=r._self._c;return t("svg",{attrs:{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 14 14"}},[t("defs",[t("clipPath",{attrs:{id:"a"}},[t("path",{attrs:{fill:"none",d:"M0 0h14v14H0z"}})])]),t("g",{attrs:{"clip-path":"url(#a)"}},[t("path",{attrs:{fill:"none",stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"1.5",d:"M1 12.75V1.25m5.75 0v11.5M1 6.52h5.75m6.07.92H9.95l-.44 2.65.32-.16c.37-.18.77-.28 1.19-.28h.7c.73 0 1.33.59 1.33 1.33v.44c0 .73-.59 1.33-1.33 1.33h-.88c-.58 0-1.07-.37-1.25-.88"}})])])};const s={render:e};export{s as default};
