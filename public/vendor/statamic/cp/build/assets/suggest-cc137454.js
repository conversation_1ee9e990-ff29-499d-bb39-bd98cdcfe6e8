var e=function(){var t=this,r=t._self._c;return r("svg",{attrs:{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 16 15"}},[r("g",{attrs:{fill:"none",stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round","stroke-miterlimit":"10"}},[r("circle",{attrs:{cx:"12.6",cy:"2.6",r:"2.1"}}),r("path",{attrs:{d:"m14.1 4.1 1.4 1.4"}})]),r("path",{attrs:{fill:"none",stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round","stroke-miterlimit":"10",d:"M8.5 4.5h-7c-.6 0-1-.4-1-1v-1c0-.6.4-1 1-1h7"}}),r("path",{attrs:{d:"M1.8 6.5c-.5 0-.8.3-.8.7s.3.8.8.8.8-.3.8-.8-.4-.7-.8-.7z"}}),r("path",{attrs:{fill:"none",stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round","stroke-miterlimit":"10",d:"M4 7.5h8"}}),r("path",{attrs:{d:"M1.8 10c-.5 0-.8.3-.8.8s.3.8.8.8.8-.3.8-.8-.4-.8-.8-.8z"}}),r("path",{attrs:{fill:"none",stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round","stroke-miterlimit":"10",d:"M4 11h8"}}),r("path",{attrs:{d:"M1.8 13.5c-.4 0-.8.3-.8.8s.3.7.8.7.8-.3.8-.8-.4-.7-.8-.7z"}}),r("path",{attrs:{fill:"none",stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round","stroke-miterlimit":"10",d:"M4 14.5h8"}})])};const n={render:e};export{n as default};
