var t=function(){var e=this,r=e._self._c;return r("svg",{attrs:{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"}},[r("path",{attrs:{fill:"none",stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round",d:"M.5 6.504h20m-17-2.25h0m0 0a.25.25 0 1 0 .25.25.25.25 0 0 0-.25-.25m2 0h0m0 0a.25.25 0 1 0 .25.25.25.25 0 0 0-.25-.25m2 0h0m0 0a.25.25 0 1 0 .25.25.25.25 0 0 0-.25-.25"}}),r("rect",{attrs:{width:"20",height:"15",x:".5",y:"2.504",fill:"none",stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round",rx:"2",ry:"2"}}),r("path",{attrs:{fill:"none",stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round",d:"M20.5 10.504h3m-3-4.004h1a2 2 0 0 1 2 2v11a2 2 0 0 1-2 2h-16a2 2 0 0 1-2-2v-2"}})])};const o={render:t};export{o as default};
