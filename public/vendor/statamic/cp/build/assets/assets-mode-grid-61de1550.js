var e=function(){var r=this,t=r._self._c;return t("svg",{attrs:{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"}},[t("g",{attrs:{fill:"none",stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"1.5"}},[t("rect",{attrs:{width:"9.75",height:"9.75",x:".75",y:"13.5",rx:"1",ry:"1"}}),t("path",{attrs:{d:"M.75 19.5h9.75"}}),t("rect",{attrs:{width:"9.75",height:"9.75",x:".75",y:".75",rx:"1",ry:"1"}}),t("path",{attrs:{d:"M.75 6.75h9.75"}}),t("rect",{attrs:{width:"9.75",height:"9.75",x:"13.5",y:".75",rx:"1",ry:"1"}}),t("path",{attrs:{d:"M13.5 6.75h9.75"}}),t("rect",{attrs:{width:"9.75",height:"9.75",x:"13.5",y:"13.5",rx:"1",ry:"1"}}),t("path",{attrs:{d:"M13.5 19.5h9.75"}})])])};const h={render:e};export{h as default};
