var e=function(){var r=this,t=r._self._c;return t("svg",{attrs:{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 14 14"}},[t("defs",[t("clipPath",{attrs:{id:"a"}},[t("path",{attrs:{fill:"none",d:"M0 0h14v14H0z"}})])]),t("g",{attrs:{"clip-path":"url(#a)"}},[t("path",{attrs:{fill:"none",stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"1.5",d:"M12.36 11.42H9.15c-.18 0-.32-.14-.32-.32 0-.08.03-.15.08-.21l2.92-3.34c.06-.07.14-.1.23-.1.17 0 .3.14.3.3v3.67zm0 0h.88m-.88 0v1.33M1 12.75V1.25m5.75 0v11.5M1 6.52h5.75"}})])])};const s={render:e};export{s as default};
