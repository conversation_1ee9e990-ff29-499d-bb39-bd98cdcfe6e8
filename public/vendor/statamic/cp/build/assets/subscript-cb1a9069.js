var a=function(){var e=this,r=e._self._c;return r("svg",{attrs:{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 14 14"}},[r("path",{attrs:{fill:"currentColor","fill-rule":"evenodd",d:"M7.955.519a.75.75 0 0 0-1.06.03L4.329 3.266 1.763.549a.75.75 0 1 0-1.09 1.03l2.624 2.78-3.013 3.19a.75.75 0 1 0 1.09 1.03L4.33 5.45l2.954 3.13a.75.75 0 0 0 1.09-1.03L5.361 4.358l2.624-2.78a.75.75 0 0 0-.03-1.06Zm3.674 7.917c-.708 0-1.31.453-1.532 1.084a.625.625 0 1 0 1.179.416.375.375 0 0 1 .353-.25h.693a.35.35 0 0 1 .14.67l-1.484.648a1.625 1.625 0 0 0-.974 1.49v.567c0 .346.28.625.625.625h2.667a.625.625 0 1 0 0-1.25H11.26a.375.375 0 0 1 .22-.286l1.484-.65a1.6 1.6 0 0 0-.641-3.064h-.693Z","clip-rule":"evenodd"}})])};const t={render:a};export{t as default};
