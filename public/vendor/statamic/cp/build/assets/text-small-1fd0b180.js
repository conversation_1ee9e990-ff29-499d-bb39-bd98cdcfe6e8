var a=function(){var e=this,l=e._self._c;return l("svg",{attrs:{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 14 14"}},[l("path",{attrs:{fill:"currentColor","fill-rule":"evenodd",d:"M4.386 2.152a.53.53 0 0 1 1.03 0l1.257 5.03a.75.75 0 0 0 1.455-.364l-1.257-5.03a2.03 2.03 0 0 0-3.94 0L1.177 8.802a.737.737 0 0 0-.008.031l-.996 3.985a.75.75 0 0 0 1.455.364l.858-3.432h3.415a.75.75 0 0 0 0-1.5H2.86l1.525-6.098Zm7.506 9.598.496 1.487a.75.75 0 0 0 1.423-.474l-.66-1.98a.835.835 0 0 0-.013-.04l-1.169-3.506a1.444 1.444 0 0 0-2.74 0L8.06 10.75a.792.792 0 0 0-.009.026l-.662 1.987a.75.75 0 0 0 1.423.474l.496-1.487h2.585Zm-.5-1.5L10.6 7.872l-.792 2.378h1.585Z","clip-rule":"evenodd"}})])};const t={render:a};export{t as default};
