var a=function(){var e=this,r=e._self._c;return r("svg",{attrs:{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 14 14"}},[r("path",{attrs:{fill:"currentColor","fill-rule":"evenodd",d:"M11.63.083c-.71 0-1.31.454-1.533 1.084a.625.625 0 1 0 1.179.416.375.375 0 0 1 .353-.25h.693a.35.35 0 0 1 .14.67l-1.484.649a1.625 1.625 0 0 0-.974 1.488v.569c0 .345.28.625.625.625h2.667a.625.625 0 1 0 0-1.25H11.26a.375.375 0 0 1 .22-.287l1.484-.65a1.6 1.6 0 0 0-.641-3.064h-.693ZM7.954 5.621a.75.75 0 0 0-1.06.03L4.329 8.368 1.763 5.651a.75.75 0 0 0-1.09 1.03l2.624 2.78-3.013 3.19a.75.75 0 1 0 1.09 1.03l2.955-3.128 2.955 3.128a.75.75 0 1 0 1.09-1.03l-3.013-3.19 2.624-2.78a.75.75 0 0 0-.03-1.06Z","clip-rule":"evenodd"}})])};const t={render:a};export{t as default};
