var t=function(){var e=this,r=e._self._c;return r("svg",{attrs:{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 16 16"}},[r("path",{attrs:{fill:"none",stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round","stroke-miterlimit":"10",d:"M1.1 8.7c-.3.1-.6.5-.6.9v3.8c0 .4.3.8.6.9l3 1.1c.2.1.5.1.7 0l3-1.1c.4-.1.6-.5.6-.9V9.6c0-.4-.3-.8-.6-.9l-3-1.1c-.2-.1-.5-.1-.7 0l-3 1.1z"}}),r("path",{attrs:{fill:"none",stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round","stroke-miterlimit":"10",d:"m.7 9.1 3.8 1.4 3.8-1.4m-3.8 1.4v5M15.3 2.6c-1 .3-5.8 1.9-5.8 1.9L3.7 2.6m5.8 1.9V7"}}),r("path",{attrs:{fill:"none",stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round","stroke-miterlimit":"10",d:"M3.5 5.5V3.2c0-.4.3-.8.7-.9l5-1.7c.2-.1.4-.1.6 0l5 1.7c.4.1.7.5.7.9v5.7c0 .4-.3.8-.7.9l-4.3 1.4"}})])};const n={render:t};export{n as default};
