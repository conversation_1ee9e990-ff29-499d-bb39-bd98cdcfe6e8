var t=function(){var e=this,r=e._self._c;return r("svg",{attrs:{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"}},[r("path",{attrs:{fill:"none",stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"1.5",d:"m9.775 12.368-4.17 2.095a1.973 1.973 0 0 1-2.65-.878h0a1.973 1.973 0 0 1 .878-2.65l6.174-3.1 1.183 2.355m-.74-1.476a1.973 1.973 0 0 1 .877-2.65l6.175-3.1 2.659 5.292-5.112 2.568M.75 14.694l2.205-1.109M18.822 1.192l.882-.443h0l3.547 7.059h0l-.882.443a1.974 1.974 0 0 1-2.65-.878l-1.774-3.53a1.974 1.974 0 0 1 .877-2.65z"}}),r("path",{attrs:{fill:"none",stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"1.5",d:"M15.43 11.4a3 3 0 1 1-4.03-1.33 3 3 0 0 1 4.03 1.33zm-2.68 4.35v7.5m-6.75 0 4.591-8.417M19.5 23.25l-4.591-8.417"}})])};const n={render:t};export{n as default};
