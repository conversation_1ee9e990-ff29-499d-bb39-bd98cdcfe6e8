var e=function(){var o=this,r=o._self._c;return r("svg",{attrs:{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"}},[r("circle",{attrs:{cx:"4",cy:"20",r:"3.5",fill:"none",stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round"}}),r("circle",{attrs:{cx:"4",cy:"20",r:"1.5",fill:"none",stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round"}}),r("path",{attrs:{fill:"none",stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round",d:"M.5 20V4a3.5 3.5 0 0 1 7 0v16"}}),r("path",{attrs:{fill:"none",stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round",d:"M4 23.5h18.5a1 1 0 0 0 1-1v-18a1 1 0 0 0-1-1h-15"}}),r("path",{attrs:{fill:"none",stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round",d:"M15.5 18.5H11a.5.5 0 0 1-.5-.5V9.309a.5.5 0 0 1 .724-.447L15.5 11zm0-6H20a.5.5 0 0 1 .5.5v5a.5.5 0 0 1-.5.5h-4.5m-5-6H13m-2.5 2H13m2.5-3.5V8.5m4 4V10m-4 4.5H18m-2.5 2H18"}})])};const t={render:e};export{t as default};
