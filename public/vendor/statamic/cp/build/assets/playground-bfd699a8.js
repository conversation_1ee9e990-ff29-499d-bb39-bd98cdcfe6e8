var n=function(){var o=this,r=o._self._c;return r("svg",{attrs:{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"}},[r("path",{attrs:{fill:"none",stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round",d:"M.5 22.425v-12.5a.5.5 0 0 1 .5-.5h10.5"}}),r("rect",{attrs:{width:"6",height:"2",x:"2.5",y:"18.425",fill:"none",stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round",rx:".5",ry:".5"}}),r("path",{attrs:{fill:"none",stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round",d:"M3.5 9.425v9m4-9v9m4-12h12v8.5a.5.5 0 0 1-.5.5H12a.5.5 0 0 1-.5-.5v-8.5zm2 9v7m9-7v7m-6-7v7m-3-1h3m-3-2h3m-3-2h3"}}),r("path",{attrs:{fill:"none",stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round",d:"M13.5 15.425v-4a1.5 1.5 0 0 1 3 0v4"}}),r("circle",{attrs:{cx:"20",cy:"11.425",r:"1.5",fill:"none",stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round"}}),r("path",{attrs:{fill:"none",stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round",d:"m23.5 6.425-5.68-4.734a.5.5 0 0 0-.64 0L11.5 6.425"}})])};const t={render:n};export{t as default};
