<svg version="1.2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 187 148" width="187" height="148">
	<title>Frame 427318262</title>
	<defs>
		<clipPath clipPathUnits="userSpaceOnUse" id="cp1">
			<path d="m-305-319h1439v786h-1439z"/>
		</clipPath>
		<linearGradient id="g1" x2="1" gradientUnits="userSpaceOnUse" gradientTransform="matrix(593.82,40.644,-38.932,568.802,284.77,32.12)">
			<stop offset=".16" stop-color="#ffdb9d"/>
			<stop offset=".95" stop-color="#9f835d"/>
		</linearGradient>
		<linearGradient id="g2" x2="1" gradientUnits="userSpaceOnUse" gradientTransform="matrix(593.82,40.645,-47.309,691.181,285.14,26.674)">
			<stop offset=".16" stop-color="#ffdb9d"/>
			<stop offset=".95" stop-color="#9f835d"/>
		</linearGradient>
		<linearGradient id="g3" x2="1" gradientUnits="userSpaceOnUse" gradientTransform="matrix(593.863,40.681,-38.287,558.919,285.437,22.193)">
			<stop offset=".16" stop-color="#ffdb9d"/>
			<stop offset=".95" stop-color="#9f835d"/>
		</linearGradient>
		<linearGradient id="g4" x2="1" gradientUnits="userSpaceOnUse" gradientTransform="matrix(593.859,40.645,-38.783,566.646,286.141,11.86)">
			<stop offset=".16" stop-color="#ffdb9d"/>
			<stop offset=".95" stop-color="#9f835d"/>
		</linearGradient>
		<linearGradient id="g5" x2="1" gradientUnits="userSpaceOnUse" gradientTransform="matrix(593.861,40.645,-196.689,2873.812,286.919,.378)">
			<stop offset=".16" stop-color="#ffdb9d"/>
			<stop offset=".95" stop-color="#9f835d"/>
		</linearGradient>
		<linearGradient id="g6" x2="1" gradientUnits="userSpaceOnUse" gradientTransform="matrix(593.821,40.644,-38.256,558.935,286.549,6.082)">
			<stop offset=".16" stop-color="#ffdb9d"/>
			<stop offset=".95" stop-color="#9f835d"/>
		</linearGradient>
		<linearGradient id="g7" x2="1" gradientUnits="userSpaceOnUse" gradientTransform="matrix(593.856,40.645,-75,1095.805,284.584,34.452)">
			<stop offset=".16" stop-color="#ffdb9d"/>
			<stop offset=".95" stop-color="#9f835d"/>
		</linearGradient>
		<linearGradient id="g8" x2="1" gradientUnits="userSpaceOnUse" gradientTransform="matrix(593.858,40.644,-74.517,1088.782,285.622,19.305)">
			<stop offset=".16" stop-color="#ffdb9d"/>
			<stop offset=".95" stop-color="#9f835d"/>
		</linearGradient>
		<linearGradient id="g9" x2="1" gradientUnits="userSpaceOnUse" gradientTransform="matrix(593.862,40.682,-41.148,600.663,284.918,29.489)">
			<stop offset=".16" stop-color="#ffdb9d"/>
			<stop offset=".95" stop-color="#9f835d"/>
		</linearGradient>
		<linearGradient id="g10" x2="1" gradientUnits="userSpaceOnUse" gradientTransform="matrix(593.856,40.644,-41.114,600.721,286.734,3.045)">
			<stop offset=".16" stop-color="#ffdb9d"/>
			<stop offset=".95" stop-color="#9f835d"/>
		</linearGradient>
		<linearGradient id="g11" x2="1" gradientUnits="userSpaceOnUse" gradientTransform="matrix(593.863,40.645,-95.343,1393.05,285.807,16.748)">
			<stop offset=".16" stop-color="#ffdb9d"/>
			<stop offset=".95" stop-color="#9f835d"/>
		</linearGradient>
		<linearGradient id="g12" x2="1" gradientUnits="userSpaceOnUse" gradientTransform="matrix(593.863,40.644,-25.658,374.892,285.807,16.601)">
			<stop offset=".16" stop-color="#ffdb9d"/>
			<stop offset=".95" stop-color="#9f835d"/>
		</linearGradient>
		<linearGradient id="g13" x2="1" gradientUnits="userSpaceOnUse" gradientTransform="matrix(593.817,40.644,-71.347,1042.393,286.623,5.008)">
			<stop offset=".16" stop-color="#ffdb9d"/>
			<stop offset=".95" stop-color="#9f835d"/>
		</linearGradient>
		<linearGradient id="g14" x2="1" gradientUnits="userSpaceOnUse" gradientTransform="matrix(593.859,40.644,-50.105,732.095,284.881,30.082)">
			<stop offset=".16" stop-color="#ffdb9d"/>
			<stop offset=".95" stop-color="#9f835d"/>
		</linearGradient>
		<linearGradient id="g15" x2="1" gradientUnits="userSpaceOnUse" gradientTransform="matrix(-49.254,161.586,-166.96,-50.892,73.152,-18.256)">
			<stop offset=".16" stop-color="#ffdb9d"/>
			<stop offset=".95" stop-color="#9f835d"/>
		</linearGradient>
		<linearGradient id="g16" x2="1" gradientUnits="userSpaceOnUse" gradientTransform="matrix(0,630,0,0,NaN,NaN)">
			<stop offset=".16" stop-color="#ffdb9d"/>
			<stop offset=".95" stop-color="#ffffff"/>
		</linearGradient>
	</defs>
	<style>
		.s0 { fill: url(#g1) } 
		.s1 { fill: url(#g2) } 
		.s2 { fill: url(#g3) } 
		.s3 { fill: url(#g4) } 
		.s4 { fill: url(#g5) } 
		.s5 { fill: url(#g6) } 
		.s6 { fill: #ffffff } 
		.s7 { fill: url(#g7) } 
		.s8 { fill: url(#g8) } 
		.s9 { fill: url(#g9) } 
		.s10 { fill: url(#g10) } 
		.s11 { fill: url(#g11) } 
		.s12 { fill: url(#g12) } 
		.s13 { fill: url(#g13) } 
		.s14 { fill: url(#g14) } 
		.s15 { fill: url(#g15) } 
		.s16 { fill: none;stroke: url(#g16);stroke-miterlimit:10;stroke-width: 3 } 
	</style>
	<g id="Clip-Path" clip-path="url(#cp1)">
		<g>
			<path class="s0" d="m375.4 50.7c-3 3.6-7.7 5.8-13.1 5.8-10.9 0-18.8-7.5-18.8-18.6 0-11 7.9-18.5 18.8-18.5 5.4 0 10.1 2.3 13.1 5.8v-5.6h6.9v36.1h-6.9zm0-6.5v-12.3c-2-4-6.5-6.6-11.9-6.6-7.2 0-13 4.8-13 12.7 0 7.9 5.8 12.8 13 12.8 5.4 0 9.9-2.6 11.9-6.5z"/>
			<path class="s1" d="m434.8 49.2l4.4-4.4c2.4 3.3 6.9 6 11.9 6 5.1 0 8.9-1.9 8.9-5.5 0-2.9-2.9-4.4-7.6-5.1l-3.3-0.5c-6.9-1-11.3-4.1-11.3-9.8 0-5.7 5-10.4 14.3-10.4 5.9 0 10.5 2.4 13.4 5.7l-4.3 4.4c-2.5-2.7-5.5-4.3-9.3-4.3-4.7 0-7 2.1-7 4.5 0 2.4 2.1 3.6 5.6 4.1l3.5 0.5c8.3 1.1 13.1 4.7 13.1 10.8 0 6.3-6 11.4-16.1 11.4-7.3 0-12.9-3.4-16.1-7.4z"/>
			<path class="s2" d="m497.1 38c0-10.3 7.6-18.5 19.8-18.5 12.3 0 19.8 8.2 19.8 18.5 0 10.3-7.6 18.6-19.8 18.6-12.2 0-19.8-8.3-19.8-18.6zm32.5 0c0-7.8-5.6-12.7-12.7-12.7-7.1 0-12.7 4.9-12.7 12.7 0 7.9 5.6 12.8 12.7 12.8 7.1 0 12.7-4.9 12.7-12.8z"/>
			<path class="s3" d="m674 50.7c-3 3.6-7.7 5.8-13.1 5.8-10.8 0-18.8-7.5-18.8-18.6 0-11 8-18.5 18.8-18.5 5.4 0 10.1 2.3 13.1 5.8v-5.5h6.9v36h-6.9zm0-6.5v-12.3c-2-4-6.5-6.6-11.9-6.6-7.2 0-12.9 4.8-12.9 12.7 0 7.9 5.7 12.8 12.9 12.8 5.4 0 9.9-2.6 11.9-6.5z"/>
			<path class="s4" d="m718.3 4.5h7.4v7.7h-7.4zm0.3 15.2h6.9v36.1h-6.9z"/>
			<path class="s5" d="m734.1 38c0-10.3 7.5-18.5 19.7-18.5 12.3 0 19.8 8.2 19.8 18.5 0 10.3-7.5 18.6-19.8 18.6-12.2 0-19.7-8.3-19.7-18.6zm32.5 0c0-7.8-5.6-12.7-12.8-12.7-7.1 0-12.7 4.9-12.7 12.7 0 7.9 5.6 12.8 12.7 12.8 7.2 0 12.8-4.9 12.8-12.8z"/>
			<path class="s6" d="m279.8 129.6l5-4.1c2.9 4.6 8 9.2 16.5 9.2 8 0 12.1-4 12.1-8.7 0-4.4-3.7-6.9-10.2-7.8l-4.9-0.7c-8.7-1.3-14.5-5.9-14.5-12.9 0-7.1 6.6-13.7 17.9-13.7 7.9 0 14.2 3.3 17.6 8l-4.8 4.3c-3.3-4.2-7.9-6.1-12.8-6.1-6.6 0-10.6 3.2-10.6 7 0 3.7 3.2 6.2 8.3 6.9l5 0.7c10.2 1.5 16.4 6.1 16.4 13.9 0 8.7-7.6 15.2-19.5 15.2-11.9 0-17.9-5.8-21.5-11.2z"/>
			<path class="s6" d="m516.9 89.6h7.4v7.7h-7.4zm0.3 15.7h6.9v35.6h-6.9z"/>
			<path class="s7" d="m325.4 30.6v-3.1h15.1v-8h-0.9c-5.9 0-9.9 1.7-12.1 5.6v-5.5h-6.9v36.2h6.9v-25.2h-2.1z"/>
			<path class="s8" d="m550.2 30.6v-3.1h15.1v-7.8h-0.8c-5.9 0-10 1.5-12.2 5.5v-5.5h-6.8v36.1h6.8v-25.2h-2.1z"/>
			<path class="s9" d="m412.8 19.5c-6.5 0-10.7 3-12.7 6.1v-6h-6.9v36.2h6.9v-25.3h-2.3v-3h12.3c0 0 6-0.3 8 1.1 3.2 2.3 3 6.2 3 10.9v16.3h6.9v-20.7c0-9.6-5.3-15.6-15.3-15.6z"/>
			<path class="s6" d="m343.5 141c6.5 0 10.7-3 12.7-6.2v6.1h6.9v-36.2h-6.9v25.2h2.3v3.1h-12.3c0 0-6 0.3-8-1.2-3.2-2.3-3-6.1-3-10.8v-16.4h-6.9v20.7c0 9.7 5.3 15.6 15.3 15.6z"/>
			<path class="s10" d="m801.7 19.5c-6.4 0-10.6 3-12.6 6.1v-6h-6.9v36.2h6.9v-25.3h-2.3v-3h12.3c0 0 6-0.3 8 1.1 3.1 2.3 3 6.2 3 10.9v16.3h6.9v-20.7c0-9.6-5.3-15.6-15.3-15.6z"/>
			<path class="s11" d="m492.4 10.4h1.1v-5.4c-0.7 0-1.8 0-2.7 0-8.3 0-12.5 4.7-12.5 11.3v3.4h-6.4v5.3h6.4v30.7h6.9v-27.7h-3.1v-3h3.1 8.3v-5.3h-8.3v-3.3c0-4.2 2.3-6.1 7.2-6.1z"/>
			<path class="s12" d="m616.6 19.6c-6.3 0-10.5 2.9-12.5 5.9-2.5-3.8-6.8-5.9-12.7-5.9-6.5 0-10.7 3-12.7 6.1v-6h-6.9v36.2h6.9v-25.3h-2.3v-3h12.3c0 0 6-0.3 8 1.1 3.2 2.3 3 6.2 3 10.9v16.3h6.9v-20.7c0-2.9-0.5-5.4-1.5-7.6h8.8c0 0 6-0.3 8 1.2 3.2 2.3 3 6.1 3 10.8v16.4h6.9v-20.7c0-9.7-5.3-15.6-15.3-15.6z"/>
			<path class="s6" d="m419.4 104.5c-6.3 0-10.5 2.8-12.5 5.9-2.5-3.8-6.8-5.9-12.7-5.9-6.5 0-10.7 3-12.7 6.1v-6h-6.9v36.2h6.9v-25.3h-2.3v-3h12.3c0 0 6-0.3 8 1.1 3.2 2.3 3 6.2 3 10.9v16.3h6.9v-20.7c0-2.9-0.5-5.4-1.5-7.6h8.8c0 0 6-0.3 8 1.2 3.2 2.3 3 6.1 3 10.8v16.4h6.9v-20.7c0-9.7-5.3-15.6-15.3-15.6z"/>
			<path class="s6" d="m489 104.5c-6.3 0-10.4 2.8-12.5 5.9-2.5-3.8-6.7-5.9-12.7-5.9-6.4 0-10.6 3-12.6 6.1v-6h-6.9v36.2h6.9v-25.3h-2.3v-3h12.3c0 0 6-0.3 7.9 1.1 3.2 2.3 3 6.2 3 10.9v16.3h6.9v-20.7c0-2.9-0.5-5.4-1.4-7.6h8.7c0 0 6-0.3 8 1.2 3.2 2.3 3 6.1 3 10.8v16.4h6.9v-20.7c0-9.7-5.3-15.6-15.3-15.6z"/>
			<path class="s13" d="m710.9 25v-5.3h-9.4v-8.7h-6.8v8.7h-6.8v5.3h6.7v18.3c0 8.8 4.5 12.8 13.3 12.8 1 0 2.2-0.1 3-0.3v-5.2h-1.2c-5.7 0-8.2-1.9-8.2-7.1v-15.6h-3.2v-3h4.1 8.5z"/>
			<path class="s6" d="m556.5 110.4v-5.2h-9.5v-9.3h-6.7v9.3h-6.8v5.2h6.7v17.8c0 8.9 4.5 12.9 13.2 12.9 1.1 0 2.2-0.2 3.1-0.3v-5.3h-1.2c-5.7 0-8.2-1.8-8.2-7v-15.1h-3.2v-3h4.1 8.5z"/>
			<path class="s14" d="m279.8 7.5v5.8h16.5v42.4h7v-39.3h-2.3v-3.1h10.6 8.2v-5.8z"/>
			<path class="s15" d="m10 6.2c1.1 0.7 1.9 1.2 2.6 1.7 25.2 17.9 50.4 35.8 75.6 53.8 1.1 0.8 2.9 2.2 4.5 3l0.3 34.3c0 0-1.7 2.8-1.8 2.8-8.1 14.2-16.2 26.3-24.2 40.4-8.6-3.7-16.9-7.3-25.2-11-0.8-0.4-1.3-1.6-1.5-2.5-1.2-7-2.5-14.1-3.4-21.2-0.2-1 0.6-2.5 1.5-3.3 4.5-3.8 9.1-7.5 14.4-11.7-3.3-0.7-5.8-1.2-8.3-1.7-3.6-0.8-8-0.5-10.5-2.5-2.5-2-3.2-6.3-4.6-9.6-6.2-14.8-12.4-29.7-18.6-44.5-0.4-0.8-0.8-1.8-0.8-2.7 0-8.2 0-16.4 0-25.3zm92.9 97.8c13.8 9.3 27.7 18.6 42.2 28.3-7.5 3.3-14.3 6.2-21.5 9.4-7.1-12.4-14.2-24.8-21.2-37.1 0.1-0.1 0.3-0.4 0.5-0.6zm-57.3 25.2c7 3 13.3 5.8 20 8.7 6.7-11.7 13.1-23.1 19.6-34.5q-0.2-0.2-0.5-0.4c-12.8 8.6-25.7 17.2-39.1 26.2zm52.6-61.6c4.1 3.1 7.8 5.8 11.5 8.6 13 10.1 26 20.3 39.1 30.4 1.7 1.3 2.3 2.5 1.9 4.7-1.1 5.9-2 11.7-3.1 17.9-0.8-0.5-1.5-0.7-2.1-1.1-15.1-10.1-30.2-20.2-45.3-30.3-1.7-1.1-2.3-2.3-2.3-4.3 0.1-7.6 0-15.3 0-22.9 0-0.8 0.2-1.6 0.3-2.9zm-8.8 2c-0.8 0.2-1.1 0.3-1.3 0.4q-23.3 18-46.6 36c-0.6 0.4-1.2 1.2-1.2 1.8 0.9 6.1 1.9 12.2 3 18.5 0.7-0.4 1-0.4 1.3-0.6 14.4-9.7 28.8-19.3 43.1-29 0.9-0.7 1.6-2.2 1.6-3.3 0.1-6.9 0.1-13.8 0.1-20.7zm59.5 16.3v0.7c-4.9 1-9.8 2-14.7 2.8-0.6 0.2-1.4-0.4-2-0.8q-11-8.2-22-16.5c-0.2-0.1-0.3-0.4-0.9-1.3 13.6 5.2 26.6 10.2 39.6 15.1zm-70.8-13.5c-12 4.6-24 9.2-36.1 13.8 2.2 1 4.5 1.3 6.6 2 5.3 1.7 9.5 0.9 13.8-3 5-4.5 10.7-8.2 16.1-12.2q-0.2-0.3-0.4-0.6zm-63.3-37c7.2 17 14 33.2 20.9 49.6 16.8-6.4 33.4-12.7 50.8-19.4-24.2-10.1-47.6-20-71.7-30.2zm163.5-4.4c-7.7 18.3-15 35.8-22.5 53.6-18.2-7-36.1-13.8-54.8-20.9 26.1-11 51.3-21.7 77.3-32.7zm-69 39.8q0 0-0.1 0h0.1q0 0 0 0zm-96.1-58.3c0 5.8 0.1 10.7-0.1 15.6-0.1 2.2 0.6 3.2 2.6 4 17.2 7.2 34.4 14.5 51.6 21.8 2.4 1 4.8 2 7.3 3 0.1-0.2 0.1-0.4 0.2-0.6-20.3-14.4-40.7-28.9-61.6-43.8zm166.8-6.1c0 6.7 0.1 12.6-0.2 18.4 0 0.9-1.6 2.2-2.7 2.6-19.1 8.2-38.2 16.3-57.4 24.3-1.9 0.9-3.9 1.7-6.2 1.9 21.9-15.6 43.8-31.1 66.5-47.2zm-1.6 24.6q0 0-0.1 0 0.1-0.1 0.1-0.1zm1.6-24.6q0 0 0 0.1 0-0.1 0-0.1z"/>
			<path class="s16" d="m230.9 2.2v154"/>
		</g>
	</g>
</svg>