<?xml version="1.0" encoding="utf-8"?>
<!-- Generator: Adobe Illustrator 24.1.2, SVG Export Plug-In . SVG Version: 6.00 Build 0)  -->
<svg version="1.1" id="Vrstva_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px"
	 viewBox="0 0 1440 2425" style="enable-background:new 0 0 1440 2425;" xml:space="preserve">
<style type="text/css">
	.st0{fill:url(#SVGID_1_);}
	.st1{opacity:0.35;clip-path:url(#SVGID_3_);}
	.st2{opacity:0.2;fill:none;stroke:#C5EADD;stroke-width:4;stroke-miterlimit:10;stroke-opacity:0.4;enable-background:new    ;}
	.st3{opacity:0.7;fill:none;stroke:#FFFFFF;stroke-width:4;stroke-miterlimit:10;stroke-opacity:0.6;enable-background:new    ;}
	.st4{opacity:0.4;fill:none;stroke:#D5F7EA;stroke-width:4;stroke-miterlimit:10;stroke-opacity:0.4;enable-background:new    ;}
	.st5{opacity:0.35;clip-path:url(#SVGID_5_);}
	.st6{opacity:0.4;fill:none;stroke:#C5EADD;stroke-width:4;stroke-miterlimit:10;stroke-opacity:0.4;enable-background:new    ;}
	.st7{opacity:0.1;fill:none;stroke:#D5F7EA;stroke-width:4;stroke-miterlimit:10;stroke-opacity:0.4;enable-background:new    ;}
</style>
<radialGradient id="SVGID_1_" cx="421.3938" cy="1632.9269" r="1" gradientTransform="matrix(781.7936 1253.3158 1478.0398 -921.9719 -2742867.5 977373.25)" gradientUnits="userSpaceOnUse">
	<stop  offset="7.692310e-02" style="stop-color:#41866D"/>
	<stop  offset="0.3233" style="stop-color:#165447"/>
	<stop  offset="0.7306" style="stop-color:#12302E"/>
	<stop  offset="0.9663" style="stop-color:#141414"/>
</radialGradient>
<rect class="st0" width="1442" height="1905.7"/>
<g>
	<defs>
		<polygon id="SVGID_2_" points="-271.9,852.4 1173.4,-890.2 2267.8,14.9 822.6,1757.6 		"/>
	</defs>
	<clipPath id="SVGID_3_">
		<use xlink:href="#SVGID_2_"  style="overflow:visible;"/>
	</clipPath>
	<g class="st1">
		<path class="st2" d="M468.6,263.6l1.7-2.1l0.8,4.2L468.6,263.6z"/>
		<path class="st2" d="M1130.9,567.7l106.5,52.3l-87,14.8L1130.9,567.7z"/>
		<path class="st3" d="M1210.4,499.4l27,120.6l-106.5-52.3L1210.4,499.4z"/>
		<path class="st3" d="M1290.6,573.5l-53.1,46.5l-27-120.6L1290.6,573.5z"/>
		<path class="st3" d="M1130.9,567.7l-75.7-25.9l78.4-30.6L1130.9,567.7z"/>
		<path class="st3" d="M1210.4,499.4l-79.5,68.3l2.7-56.5L1210.4,499.4z"/>
		<path class="st3" d="M1524.3,459.9l-97.6,83.3l4.3-91L1524.3,459.9z"/>
		<path class="st3" d="M1133.7,511.2l-78.4,30.6l69.3-112.7L1133.7,511.2z"/>
		<path class="st3" d="M1124.5,429.1l85.9,70.3l-76.8,11.8L1124.5,429.1z"/>
		<path class="st2" d="M1055.2,541.8l-11.3-118.3l80.6,5.6L1055.2,541.8z"/>
		<path class="st2" d="M1043.9,423.5L970,436.1l62.7-115L1043.9,423.5z"/>
		<path class="st4" d="M970,436.1l-29.3-126.6l92,11.6L970,436.1z"/>
		<path class="st2" d="M940.8,309.4l-98.6,10.2l91.9-93.8L940.8,309.4z"/>
		<path class="st2" d="M842.1,319.6l-1.3-84.4l93.2-9.5L842.1,319.6z"/>
		<path class="st4" d="M840.9,235.3l-4.4-101.7l97.6,92.2L840.9,235.3z"/>
		<path class="st4" d="M687.8,31.4l59.3,5.5l12.9,64.8L687.8,31.4z"/>
		<path class="st2" d="M470.4,261.5l-1.7,2.1"/>
		<path class="st2" d="M687.8,31.4l24-79.7l35.4,85.2L687.8,31.4z"/>
		<path class="st2" d="M747.1,36.9l-35.4-85.2l110.1,30.5L747.1,36.9z"/>
		<path class="st4" d="M760,101.7l-12.9-64.8L842,67.3L760,101.7z"/>
		<path class="st4" d="M836.5,133.6L760,101.7l82-34.4L836.5,133.6z"/>
		<path class="st3" d="M747.1,36.9l74.7-54.7L842,67.3L747.1,36.9z"/>
		<path class="st2" d="M934.1,225.8l-97.6-92.2L943,115.1L934.1,225.8z"/>
		<path class="st2" d="M943,115.1l-106.5,18.5l5.5-66.3L943,115.1z"/>
		<path class="st2" d="M1026.8,231.4l-86.1,78l-6.7-83.6L1026.8,231.4z"/>
		<path class="st2" d="M1032.8,321.1l-92-11.6l86.1-78L1032.8,321.1z"/>
		<path class="st2" d="M934.1,225.8l9-110.7l83.8,116.3L934.1,225.8z"/>
		<path class="st2" d="M1032.8,321.1l-5.9-89.6l63.1,6.2L1032.8,321.1z"/>
		<path class="st3" d="M1124.5,429.1l-80.6-5.6l106.8-73.4L1124.5,429.1z"/>
		<path class="st4" d="M1150.6,350.1l-106.8,73.4l-11.1-102.5L1150.6,350.1z"/>
		<path class="st2" d="M1150.6,350.1l-117.9-29l57.1-83.4L1150.6,350.1z"/>
		<path class="st2" d="M1150,634.8l-121.3,7.3l26.7-100.2L1150,634.8z"/>
		<path class="st3" d="M1124.5,429.1l26.2-79l61.3,19.8L1124.5,429.1z"/>
		<path class="st3" d="M1244.4,460.4l-33.9,39l-85.9-70.3L1244.4,460.4z"/>
		<path class="st3" d="M1211.9,369.9l32.4,90.5l-119.9-31.3L1211.9,369.9z"/>
		<path class="st3" d="M1244.4,460.4l46.2,113.1l-80.1-74.1L1244.4,460.4z"/>
		<path class="st2" d="M1321.8,455.4l-31.2,118.1l-46.2-113.1L1321.8,455.4z"/>
		<path class="st2" d="M1343.7,527l-53.1,46.5l31.2-118.1L1343.7,527z"/>
		<path class="st2" d="M1290.6,573.5l53.1-46.5"/>
		<path class="st3" d="M1385.2,660.9l41.3-116.8"/>
		<path class="st4" d="M1429.4,452l-85.7,75l-21.9-71.6L1429.4,452z"/>
		<path class="st4" d="M1335.4,364.7l-91,95.7l-32.4-90.5L1335.4,364.7z"/>
		<path class="st4" d="M1321.8,455.4l-77.4,5l91-95.7L1321.8,455.4z"/>
		<path class="st4" d="M1335.4,364.7l94,87.3l-107.6,3.4L1335.4,364.7z"/>
		<path class="st4" d="M1290.8,573.5l94,87.3l-107.6,3.4L1290.8,573.5z"/>
		<path class="st2" d="M1211.9,369.9l-61.3-19.8l89.9-72L1211.9,369.9z"/>
		<path class="st4" d="M1240.5,278.1l94.9,86.6l-123.5,5.2L1240.5,278.1z"/>
		<path class="st4" d="M1240.5,278.1l-89.9,72l17.5-106.1L1240.5,278.1z"/>
		<path class="st3" d="M1168.1,244l-17.5,106.1l-60.7-112.4L1168.1,244z"/>
		<path class="st4" d="M1168.1,244l-78.2-6.3l51.6-87.9L1168.1,244z"/>
		<path class="st4" d="M1026.8,231.4L943,115.1l125,14.7L1026.8,231.4z"/>
		<path class="st3" d="M1089.9,237.7l-63.1-6.2l41.2-101.5L1089.9,237.7z"/>
		<path class="st3" d="M1089.9,237.7l-21.8-107.8l73.5,19.8L1089.9,237.7z"/>
		<path class="st4" d="M1068.1,129.9l-125-14.7l115.4-51.6L1068.1,129.9z"/>
		<path class="st4" d="M943,115.1L842,67.3l146-18.3L943,115.1z"/>
		<path class="st4" d="M1058.4,63.6L943,115.1l45-66.1L1058.4,63.6z"/>
		<path class="st3" d="M1278.1,664.5l-69.4,52.9l28.6-97.1L1278.1,664.5z"/>
		<path class="st4" d="M885.7-41.7l78.6-2L988.1,49L885.7-41.7z"/>
		<path class="st4" d="M842,67.3l43.7-109.1L988.1,49L842,67.3z"/>
		<path class="st3" d="M842,67.3l-20.2-85.2l63.9-23.9L842,67.3z"/>
		<path class="st3" d="M821.8-17.8l59-103.7l4.9,79.8L821.8-17.8z"/>
		<path class="st4" d="M711.8-48.3l90-78.8l20.1,109.3L711.8-48.3z"/>
		<path class="st3" d="M885.7-41.7l86-95.3l-7.5,93.4L885.7-41.7z"/>
		<path class="st4" d="M988.1,49l101.6-44.8l-31.2,59.4L988.1,49z"/>
		<path class="st4" d="M988.1,49l-23.8-92.7l125.3,47.9L988.1,49z"/>
		<path class="st3" d="M964.3-43.7l89.7-41.1l35.7,89L964.3-43.7z"/>
		<path class="st3" d="M1058.4,63.6l113.3-29.7l-103.6,96L1058.4,63.6z"/>
		<path class="st3" d="M1068.1,129.9l103.6-96l-30.1,115.9L1068.1,129.9z"/>
		<path class="st3" d="M1089.6,4.2l82.1,29.7l-113.3,29.7L1089.6,4.2z"/>
		<path class="st4" d="M1141.5,149.7l86.1-46.5L1168.1,244L1141.5,149.7z"/>
		<path class="st4" d="M1385.6,659.9l64.3-26.9l-61.1,106.2L1385.6,659.9z"/>
		<path class="st4" d="M1171.7,33.8l56,69.3l-86.1,46.5L1171.7,33.8z"/>
		<path class="st4" d="M1287.7,141.9l-47.2,136.2l-72.4-34.1L1287.7,141.9z"/>
		<path class="st4" d="M1227.7,103.2l60,38.7l-119.6,102L1227.7,103.2z"/>
		<path class="st4" d="M1323.3,237.7l12.1,126.9l-94.9-86.6L1323.3,237.7z"/>
		<path class="st3" d="M1323.3,237.7l-82.8,40.4l47.2-136.2L1323.3,237.7z"/>
		<path class="st3" d="M1404.4,250.8l-69,113.9l-12.1-126.9L1404.4,250.8z"/>
		<path class="st4" d="M1446,346.6L1429.4,452l-94-87.3L1446,346.6z"/>
		<path class="st3" d="M1446,346.6l-110.7,18.1l69-113.9L1446,346.6z"/>
		<path class="st2" d="M1516,376.2l-86.6,75.8l16.6-105.4L1516,376.2z"/>
		<path class="st3" d="M1426.1,543.8l-85.7,75.5l3.4-92.4L1426.1,543.8z"/>
		<path class="st4" d="M1510.4,236.9L1446,346.6l-41.6-95.8L1510.4,236.9z"/>
		<path class="st3" d="M1513.8,316.7l-67.7,29.9l64.3-109.7L1513.8,316.7z"/>
		<path class="st3" d="M1388.8,739.2l-67.7,29.9l64.3-109.7L1388.8,739.2z"/>
		<path class="st4" d="M1323.3,237.7l121.3-32.7l-40.1,45.8L1323.3,237.7z"/>
		<path class="st4" d="M1444.6,205l65.8,31.9l-105.9,13.9L1444.6,205z"/>
		<path class="st4" d="M1418.8,95.3l25.8,109.7l-121.3,32.7L1418.8,95.3z"/>
		<path class="st4" d="M1287.7,141.9l67.8-54l-32.2,149.7L1287.7,141.9z"/>
		<path class="st4" d="M1355.5,88l63.3,7.4l-95.5,142.4L1355.5,88z"/>
		<path class="st4" d="M1227.7,103.2l83.9-86.9l-23.8,125.7L1227.7,103.2z"/>
		<path class="st4" d="M1311.6,16.3l43.9,71.7l-67.8,54L1311.6,16.3z"/>
		<path class="st3" d="M1171.7,33.8l66.5-50.2l-10.5,119.6L1171.7,33.8z"/>
		<path class="st3" d="M1238.2-16.4l73.3,32.6l-83.9,86.9L1238.2-16.4z"/>
		<path class="st4" d="M1089.6,4.2l112.7-113.3l-30.6,143L1089.6,4.2z"/>
		<path class="st4" d="M1202.3-109.1l35.9,92.8l-66.5,50.2L1202.3-109.1z"/>
		<path class="st4" d="M1054-84.8l74.8-37.4L1089.6,4.2L1054-84.8z"/>
		<path class="st4" d="M1128.7-122.2l73.6,13.1L1089.6,4.2L1128.7-122.2z"/>
		<path class="st2" d="M1205.9,1031.8l79-21.3l-43.5,77.3L1205.9,1031.8z"/>
		<path class="st3" d="M1190.2,925.1l94.6,85.4l-79,21.3L1190.2,925.1z"/>
		<path class="st4" d="M1355.5,88l91.6-85.6l-28.3,92.9L1355.5,88z"/>
		<path class="st4" d="M1311.6,16.3l135.6-13.8L1355.5,88L1311.6,16.3z"/>
		<path class="st2" d="M1284.9,1010.5l43.7,80.4l-87.2-3.1L1284.9,1010.5z"/>
		<path class="st4" d="M1418.8,95.3l92.5-42.2L1444.6,205L1418.8,95.3z"/>
		<path class="st3" d="M1447.1,2.4l64.2,50.7l-92.5,42.2L1447.1,2.4z"/>
		<path class="st4" d="M1511.3,53.1l53.7,61.2L1444.6,205L1511.3,53.1z"/>
		<path class="st2" d="M1390.9,975.1l-62.3,115.8l-43.7-80.4L1390.9,975.1z"/>
		<path class="st2" d="M1492.8-124.3l49.5,53.1l-95.1,73.7L1492.8-124.3z"/>
		<path class="st2" d="M1284.9,1010.5l55-121.3l51.1,85.9L1284.9,1010.5z"/>
		<path class="st3" d="M1268.9,899l16,111.5l-94.6-85.4L1268.9,899z"/>
		<path class="st3" d="M1268.9,899l71-9.8l-55,121.3L1268.9,899z"/>
		<path class="st4" d="M1458.4,779.4l99.1-63.2l-108.3-83.5L1458.4,779.4z"/>
		<path class="st2" d="M1458.2,780l-68.4-41.5l7.3,80.1L1458.2,780z"/>
		<path class="st4" d="M1449.2,632.7l74.7-26l-96.9-61.2L1449.2,632.7z"/>
		<path class="st4" d="M947.5,905.4l64.2,91.8l7.2-107.9L947.5,905.4z"/>
		<path class="st4" d="M1011.7,997.1l83.4-47.3l-76.3-60.7L1011.7,997.1z"/>
		<path class="st3" d="M1011.7,997.1l117.8,31l-34.4-78.3L1011.7,997.1z"/>
		<path class="st4" d="M1129.5,1028.2l60.7-103l-95.1,24.7L1129.5,1028.2z"/>
		<path class="st4" d="M1095.1,949.9l33-103.4l-109.3,42.7L1095.1,949.9z"/>
		<path class="st4" d="M1095.1,949.9l95.1-24.7l-62.1-78.8L1095.1,949.9z"/>
		<path class="st4" d="M1018.9,889.2l109.3-42.7l-115.3-32.1L1018.9,889.2z"/>
		<path class="st4" d="M1128.1,846.5l-32.6-93.1l-82.7,61L1128.1,846.5z"/>
		<path class="st4" d="M1128.1,846.5l80.4-129.1l-113,36L1128.1,846.5z"/>
		<path class="st4" d="M1128.1,846.5l62.1,78.8L1177.6,820L1128.1,846.5z"/>
		<path class="st4" d="M1177.6,820l31-102.7l-80.4,129.1L1177.6,820z"/>
		<path class="st4" d="M1190.2,925.2l131.1-155.9L1177.6,820L1190.2,925.2z"/>
		<path class="st4" d="M1321.4,769.3l-112.8-52l-31,102.7L1321.4,769.3z"/>
		<path class="st3" d="M1321.4,769.3l18.4,119.9l-71,9.8L1321.4,769.3z"/>
		<path class="st3" d="M1311.6,16.3l48.6-105.3l87,91.5L1311.6,16.3z"/>
		<path class="st4" d="M1311.6,16.3l-19.5-190.6l30.2-133.5l-17-115.7l-104.3-10"/>
		<path class="st2" d="M1339.9,889.2l58.2-71.5l-75.1-48.6l-44.9-104.6l-17.8,76.3l-82.8,79.3l83,22.1l8.3,56.8l46.8,44.7
			L1339.9,889.2z"/>
		<path class="st2" d="M1150,634.8l58.5,82.6l-76-21.5L1150,634.8z"/>
		<path class="st2" d="M1095.5,753.3l37-57.5l-103.8-53.7L1095.5,753.3z"/>
		<path class="st4" d="M687.2,1633.6l59.3,5.5l12.9,64.8L687.2,1633.6z"/>
		<path class="st4" d="M687.2,1633.6l24-79.7l35.4,85.2L687.2,1633.6z"/>
		<path class="st4" d="M746.5,1639.1l-35.4-85.2l110.1,30.5L746.5,1639.1z"/>
		<path class="st4" d="M759.4,1703.9l-12.9-64.8l94.9,30.5L759.4,1703.9z"/>
		<path class="st2" d="M835.9,1735.8l-76.5-31.9l82-34.3L835.9,1735.8z"/>
		<path class="st4" d="M746.5,1639.1l74.7-54.7l20.2,85.2L746.5,1639.1z"/>
		<path class="st2" d="M841.4,1669.5l-20.2-85.2l63.9-23.9L841.4,1669.5z"/>
		<path class="st2" d="M821.2,1584.4l59-103.7l4.9,79.8L821.2,1584.4z"/>
		<path class="st4" d="M711.2,1553.8l90-78.8l20.1,109.3L711.2,1553.8z"/>
		<path class="st4" d="M821.2,1584.4l-20.1-109.3l79.1,5.6L821.2,1584.4z"/>
		<path class="st4" d="M801.1,1475.1l103.7-90.8l-24.6,96.3L801.1,1475.1z"/>
		<path class="st2" d="M880.2,1480.6l24.6-96.3l66.3,80.8L880.2,1480.6z"/>
		<path class="st2" d="M880.2,1480.6l90.9-15.5l-86,95.4L880.2,1480.6z"/>
		<path class="st2" d="M1129,1028.1l75.6,4.4l-98.7,101.5L1129,1028.1z"/>
		<path class="st2" d="M885.1,1560.5l86-95.4l-7.5,93.4L885.1,1560.5z"/>
		<path class="st2" d="M971.2,1465.1l-66.3-80.8l141.6,16.3L971.2,1465.1z"/>
		<path class="st2" d="M904.8,1384.3l87.6-74.9l54,91.2L904.8,1384.3z"/>
		<path class="st2" d="M1046.4,1400.6l-54-91.2l64.4,34L1046.4,1400.6z"/>
		<path class="st4" d="M992.4,1309.5l89.7-93.7l-25.3,127.7L992.4,1309.5z"/>
		<path class="st2" d="M936.6,1240.5l90.5-66L993,1308.6L936.6,1240.5z"/>
		<path class="st4" d="M1056.8,1343.4l25.3-127.7l70.3,76.1L1056.8,1343.4z"/>
		<path class="st2" d="M773.9,208.4l66.3,26.2l-60.4,38.6L773.9,208.4z"/>
		<path class="st4" d="M760.7,101.9l79.5,132.6l-66.3-26.2L760.7,101.9z"/>
		<path class="st2" d="M840.5,234.6l1.5,84.8l-62.3-46.1L840.5,234.6z"/>
		<path class="st4" d="M787.7,1264.1l-34.8-44.6l41.5-99L787.7,1264.1z"/>
		<path class="st4" d="M887.2,1126l-99.4,138l6.7-143.6L887.2,1126z"/>
		<path class="st4" d="M794.4,1120.4l110.7-93.6l-17.9,99.3L794.4,1120.4z"/>
		<path class="st2" d="M887.2,1126l17.9-99.3l106.2-29.2L887.2,1126z"/>
		<path class="st4" d="M1011.3,997.6l-106.2,29.2l30.2-54.1L1011.3,997.6z"/>
		<path class="st4" d="M935.3,972.7l-30.2,54.1L891,957.1L935.3,972.7z"/>
		<path class="st4" d="M801.4,1474.8l-124.1-11.2l115.4-51.6L801.4,1474.8z"/>
		<path class="st2" d="M792.6,1412.1l-115.4,51.6l45-66.1L792.6,1412.1z"/>
		<path class="st2" d="M722.3,1397.5l101.6-44.8l-31.2,59.4L722.3,1397.5z"/>
		<path class="st2" d="M722.3,1397.5l7.7-95.2l93.9,50.3L722.3,1397.5z"/>
		<path class="st4" d="M730,1302.4l58.3-38.7l35.6,89L730,1302.4z"/>
		<path class="st4" d="M823.9,1352.7l81.2,31.4l-112.5,27.9L823.9,1352.7z"/>
		<path class="st2" d="M823.9,1352.7l112.7-113.3l-31.5,144.5L823.9,1352.7z"/>
		<path class="st2" d="M788.2,1263.7l74.8-37.4l-39.1,126.4L788.2,1263.7z"/>
		<path class="st3" d="M863,1226.3l73.6,13.1l-112.7,113.3L863,1226.3z"/>
		<path class="st3" d="M940.4,1145.2l-3.8,94.1l-73.6-13.1L940.4,1145.2z"/>
		<path class="st3" d="M940.4,1145.2l86,28.9l-89.8,65.2L940.4,1145.2z"/>
		<path class="st4" d="M1026.4,1173.7l78.4-39.9l-22,81.9L1026.4,1173.7z"/>
		<path class="st3" d="M991.1,1087l35.2,87.2l-86-28.9L991.1,1087z"/>
		<path class="st3" d="M991.1,1087l138.5-59.3l-103.3,146.5L991.1,1087z"/>
		<path class="st4" d="M1152.4,1291.8l-70.3-76.1l87.1,22.6L1152.4,1291.8z"/>
		<path class="st2" d="M1169.2,1238.4l101.8-36.8l-118.6,90.2L1169.2,1238.4z"/>
		<path class="st4" d="M1082.1,1215.7l159.3-126.9l-72.2,149.5L1082.1,1215.7z"/>
		<path class="st2" d="M1241.4,1088.9l29.6,112.7l-101.8,36.8L1241.4,1088.9z"/>
		<path class="st2" d="M712.2-48.4L632,5l12.5-75.2L712.2-48.4z"/>
		<path class="st2" d="M1104.8,1133.8l57.8,20.1l43.3-122.1L1104.8,1133.8z"/>
		<path class="st3" d="M1011.7,997.1l-20.6,89.8l-105.8,38.1L863,1226.3"/>
		<path class="st2" d="M632,5l55.8,26.4l24.4-79.8L632,5z"/>
		<path class="st2" d="M947.5,905.4l-12.2,67.4L891,957.1L947.5,905.4z"/>
		<path class="st4" d="M677.3,1463.6l33.9,90.2l90.6-78.9L677.3,1463.6z"/>
		<path class="st2" d="M1012.8,814.3l-65.3,91L981,799.5L1012.8,814.3z"/>
		<path class="st4" d="M1028.8,642.1l17.4,90.6l49.3,20.6L1028.8,642.1z"/>
		<path class="st2" d="M1012.8,814.3l33.3-81.6L981,799.5L1012.8,814.3z"/>
		<path class="st4" d="M1055.4,541.9L970,436.1l73.8-12.5L1055.4,541.9z"/>
	</g>
</g>
<g>
	<defs>
		
			<rect id="SVGID_4_" x="-967.6" y="-18.9" transform="matrix(0.9322 -0.362 0.362 0.9322 -420.2682 -17.6369)" width="1420.8" height="2263.1"/>
	</defs>
	<clipPath id="SVGID_5_">
		<use xlink:href="#SVGID_4_"  style="overflow:visible;"/>
	</clipPath>
	<g class="st5">
		<path class="st2" d="M-75.2,1062l97.7-67.4l-29.5,83.1L-75.2,1062z"/>
		<path class="st3" d="M-96.2,959.4l118.6,35.2l-97.7,67.4L-96.2,959.4z"/>
		<path class="st3" d="M7.7,925.7l14.8,69l-118.6-35.2L7.7,925.7z"/>
		<path class="st3" d="M22.3,666.6l25.3,125.8l-77.5-48.1L22.3,666.6z"/>
		<path class="st2" d="M-7.2,1078.1l-52.7,109.3l-74.7-72.1L-7.2,1078.1z"/>
		<path class="st3" d="M-113.8,910.8L7.7,925.7l-103.8,33.8L-113.8,910.8z"/>
		<path class="st2" d="M-80.4,840.9l88.1,84.8l-121.4-14.8L-80.4,840.9z"/>
		<path class="st2" d="M-7.1,856.7l14.8,69l-88.1-84.8L-7.1,856.7z"/>
		<path class="st2" d="M7.7,925.7l-14.8-69"/>
		<path class="st3" d="M130.2,885.7l-82-92.9"/>
		<path class="st4" d="M-30.9,745.4l23.8,111.3l-73.3-15.8L-30.9,745.4z"/>
		<path class="st4" d="M-153.1,784.8l122.2-39.4l-49.5,95.5L-153.1,784.8z"/>
		<path class="st4" d="M7.8,925.5l122.2-39.4l-49.5,95.5L7.8,925.5z"/>
		<path class="st3" d="M81.1,980.9l12.5,86.3l-71-72.3L81.1,980.9z"/>
		<path class="st4" d="M129.5,884.9l7.8-69.2l63.1,105L129.5,884.9z"/>
		<path class="st3" d="M47.8,793L72,904.5l-79.1-48L47.8,793z"/>
		<path class="st3" d="M200.4,920.7l-6.9,73.6L129,884.8L200.4,920.7z"/>
		<path class="st6" d="M-54.7,632.5L60.5,608l-38.7,58.8L-54.7,632.5z"/>
		<path class="st2" d="M367.1,1222.9l19.9-79.3l46.4,75.6L367.1,1222.9z"/>
		<path class="st3" d="M266.2,1184.5l120.8-40.9l-19.9,79.3L266.2,1184.5z"/>
		<path class="st2" d="M387,1143.6l91.6,1.1l-45.2,74.6L387,1143.6z"/>
		<path class="st2" d="M-73.1,546.9l61.6-48.3l71.8,108.6L-73.1,546.9z"/>
		<path class="st2" d="M-11.4,498.4l-84-62.5l96.6-34L-11.4,498.4z"/>
		<path class="st2" d="M407.7,1033.9l70.9,110.8l-91.6-1.1L407.7,1033.9z"/>
		<path class="st2" d="M387,1143.6l-79.3-107.1l100-2.6L387,1143.6z"/>
		<path class="st3" d="M281.7,1103.2l105.3,40.4l-120.8,40.9L281.7,1103.2z"/>
		<path class="st3" d="M281.7,1103.2l26-66.7l79.3,107.1L281.7,1103.2z"/>
		<path class="st4" d="M262.6,762.4l-112.1-24l-13.7,77.8L262.6,762.4z"/>
		<path class="st4" d="M269.5,879.7l-7-117.3l-125.8,53.8L269.5,879.7z"/>
		<path class="st2" d="M269.5,879.7l46.1-74.4l-53.1-42.8L269.5,879.7z"/>
		<path class="st2" d="M315.6,805.3l-23.1-117l-29.9,74.2L315.6,805.3z"/>
		<path class="st2" d="M315.6,805.3l27.5-83.1l-50.6-33.9L315.6,805.3z"/>
		<path class="st7" d="M292.5,688.2l7.2-99.1l-88.5,55.7L292.5,688.2z"/>
		<path class="st4" d="M292.5,688.2l-81.4-43.4l51.4,117.6L292.5,688.2z"/>
		<path class="st2" d="M269.9,880.2l-69.6,39.4l73.6,32.7L269.9,880.2z"/>
		<path class="st4" d="M262.6,762.4l-51.4-117.6l-60.7,93.6L262.6,762.4z"/>
		<path class="st4" d="M150.4,738.4l60.7-93.6l-94.5,23.6L150.4,738.4z"/>
		<path class="st4" d="M136.8,816.2l13.7-77.8L49.7,793.1L136.8,816.2z"/>
		<path class="st2" d="M150.4,738.4l-33.8-70l-93.2-1.8L150.4,738.4z"/>
		<path class="st4" d="M211.1,644.8l88.5-55.7l-139.5-29.2L211.1,644.8z"/>
		<path class="st7" d="M211.1,644.8l-50.9-84.9l-43.6,108.6L211.1,644.8z"/>
		<path class="st2" d="M116.6,668.4l43.6-108.6l-99.5,48L116.6,668.4z"/>
		<path class="st4" d="M130.6,1386.6l111.6-11.2l-90.9-58.9L130.6,1386.6z"/>
		<path class="st4" d="M242.2,1375.3l-0.7-95.8l-90.2,36.9L242.2,1375.3z"/>
		<path class="st2" d="M299.7,589.1l-46-78.6l-93.4,49.4L299.7,589.1z"/>
		<path class="st3" d="M242.2,1375.3l84.5-87.6l-85.2-8.2L242.2,1375.3z"/>
		<path class="st4" d="M326.7,1287.7l-60.4-103.1l-24.8,94.9L326.7,1287.7z"/>
		<path class="st4" d="M241.5,1279.5l-74.4-79.2l-15.9,116.1L241.5,1279.5z"/>
		<path class="st4" d="M241.5,1279.5l24.8-94.9l-99.1,15.8L241.5,1279.5z"/>
		<path class="st4" d="M151.3,1316.4l15.9-116.1l-84.3,84.9L151.3,1316.4z"/>
		<path class="st4" d="M167.2,1200.3l-97.3-16.9l13.1,101.8L167.2,1200.3z"/>
		<path class="st4" d="M167.2,1200.3l-73.7-133l-23.6,116.1L167.2,1200.3z"/>
		<path class="st4" d="M167.2,1200.3l99.1-15.8l-98.1-40.2L167.2,1200.3z"/>
		<path class="st4" d="M168.2,1144.4l-74.7-77.1l73.7,133L168.2,1144.4z"/>
		<path class="st4" d="M266.3,1184.6l-72.4-190.3l-25.8,150.1L266.3,1184.6z"/>
		<path class="st4" d="M193.9,994.3l-100.4,73.1l74.7,77.1L193.9,994.3z"/>
		<path class="st3" d="M193.9,994.2l113.8,42.4l-26,66.7L193.9,994.2z"/>
		<path class="st2" d="M307.7,1036.5L273.6,951l-79.1,41.8L81.1,980.9l58,52.7l29,110.8l59.8-61.6l53.7,20.4l61.9-19L307.7,1036.5z"
			/>
		<path class="st2" d="M-7.2,1078.1l100.7-10.8l-55.8,55.8L-7.2,1078.1z"/>
		<path class="st2" d="M69.8,1183.4l-32.2-60.3l-97.6,64.3L69.8,1183.4z"/>
		<path class="st4" d="M640.5,1968.5l33.7-49.1l63,20.3L640.5,1968.5z"/>
		<path class="st4" d="M640.5,1968.5l-58-59.8l91.8,10.7L640.5,1968.5z"/>
		<path class="st4" d="M674.2,1919.4l-91.8-10.7l80.3-81.1L674.2,1919.4z"/>
		<path class="st4" d="M737.2,1939.8l-63-20.3l72.9-67.9L737.2,1939.8z"/>
		<path class="st2" d="M802.3,1888.6l-65.2,51.1l9.9-88.2L802.3,1888.6z"/>
		<path class="st4" d="M674.2,1919.4l-11.4-91.8l84.3,23.9L674.2,1919.4z"/>
		<path class="st2" d="M747.1,1851.5l-84.3-23.9l10.3-67.3L747.1,1851.5z"/>
		<path class="st2" d="M662.8,1827.6l-61.9-102l72.2,34.7L662.8,1827.6z"/>
		<path class="st4" d="M582.5,1908.7l-25-116.8l105.3,35.7L582.5,1908.7z"/>
		<path class="st4" d="M662.8,1827.6l-105.3-35.7l43.4-66.3L662.8,1827.6z"/>
		<path class="st4" d="M557.4,1791.9l-28.8-134.7l72.2,68.4L557.4,1791.9z"/>
		<path class="st2" d="M600.9,1725.6l-72.2-68.4l103-18.5L600.9,1725.6z"/>
		<path class="st2" d="M600.9,1725.6l30.8-86.8l41.4,121.5L600.9,1725.6z"/>
		<path class="st2" d="M326.4,1288.1l40.7-63.8l40.6,135.5L326.4,1288.1z"/>
		<path class="st2" d="M673.1,1760.3l-41.4-121.5l78,52L673.1,1760.3z"/>
		<path class="st2" d="M631.6,1638.8l-103,18.5l83.3-115.5L631.6,1638.8z"/>
		<path class="st2" d="M528.6,1657.2l-22.8-112.9l106-2.6L528.6,1657.2z"/>
		<path class="st2" d="M611.9,1541.7l-106,2.6l61.1-39.6L611.9,1541.7z"/>
		<path class="st4" d="M505.9,1544.3l-38.2-123.9l99.3,84.3L505.9,1544.3z"/>
		<path class="st2" d="M418.4,1559.4l-13.6-111l100.6,95L418.4,1559.4z"/>
		<path class="st4" d="M566.9,1504.8l-99.3-84.3l100.8-24.3L566.9,1504.8z"/>
		<path class="st4" d="M366.4,1700.7l-56,8.6l-66.3-84.4L366.4,1700.7z"/>
		<path class="st4" d="M294.2,1546.7l72.2,154l-122.3-75.8L294.2,1546.7z"/>
		<path class="st4" d="M244.1,1624.9l-27.9-142.2l78,64L244.1,1624.9z"/>
		<path class="st2" d="M294.2,1546.7l-78-64l26.2-106.9L294.2,1546.7z"/>
		<path class="st4" d="M242.4,1375.8l-26.2,106.9l-32.6-52.7L242.4,1375.8z"/>
		<path class="st4" d="M183.6,1430l32.6,52.7l-67.8-21.7L183.6,1430z"/>
		<path class="st4" d="M557.3,1791.6l-70.2,102.7l11.1-125.7L557.3,1791.6z"/>
		<path class="st2" d="M498.2,1768.6l-11.1,125.7l-35.9-71.5L498.2,1768.6z"/>
		<path class="st2" d="M451.2,1822.8l10.3-110.4l36.7,56.2L451.2,1822.8z"/>
		<path class="st2" d="M451.2,1822.8l-79.5-53.1l89.8-57.3L451.2,1822.8z"/>
		<path class="st4" d="M371.7,1769.7l-5.4-69.7l95.2,12.3L371.7,1769.7z"/>
		<path class="st4" d="M461.5,1712.4l67.1-55.5l-30.4,111.7L461.5,1712.4z"/>
		<path class="st2" d="M461.5,1712.4l-44.2-153.5l111,97.9L461.5,1712.4z"/>
		<path class="st2" d="M366.3,1700.1l3.7-83.4l91.4,95.7L366.3,1700.1z"/>
		<path class="st3" d="M370.1,1616.7l47.3-57.8l44.2,153.5L370.1,1616.7z"/>
		<path class="st3" d="M336.9,1509.7l80.5,49.2l-47.3,57.8L336.9,1509.7z"/>
		<path class="st3" d="M336.9,1509.7l67.2-60.9l13.3,110.1L336.9,1509.7z"/>
		<path class="st4" d="M403.7,1448.6l3.3-87.8l60.9,59.1L403.7,1448.6z"/>
		<path class="st3" d="M310.7,1437.1l93.4,11.8l-67.2,60.9L310.7,1437.1z"/>
		<path class="st3" d="M310.7,1437.1l15.6-149.7l77.8,161.5L310.7,1437.1z"/>
		<path class="st4" d="M568.4,1396.2l-100.8,24.3l62.3-64.9L568.4,1396.2z"/>
		<path class="st2" d="M529.9,1355.6l17.4-106.7l21.1,147.4L529.9,1355.6z"/>
		<path class="st4" d="M467.6,1420.5l-33.3-200.7l95.5,135.8L467.6,1420.5z"/>
		<path class="st2" d="M434.3,1219.8l113,29.1l-17.4,106.7L434.3,1219.8z"/>
		<path class="st2" d="M407,1360.8l45.7-40.6l-85.7-97.3L407,1360.8z"/>
		<path class="st3" d="M242.2,1375.3l68.5,61.8l-18.3,110.9l77.6,68.8"/>
		<path class="st2" d="M69.1,458.3l91.1,101.5l-171.6-61.5L69.1,458.3z M69.1,458.3L1.2,401.9l-154.7-31.3"/>
		<path class="st2" d="M130.6,1386.6l53,43.5l-35.2,31L130.6,1386.6z"/>
		<path class="st4" d="M487.1,1894.3l95.4,14.4l-24.9-117.5L487.1,1894.3z"/>
		<path class="st2" d="M82.9,1285.2l47.7,101.3l-76.3-80.8L82.9,1285.2z"/>
		<path class="st4" d="M-59.9,1187.4l87.8,29l42-33L-59.9,1187.4z"/>
		<path class="st2" d="M82.9,1285.2l-55.1-68.8l26.5,89.3L82.9,1285.2z"/>
	</g>
</g>
</svg>
