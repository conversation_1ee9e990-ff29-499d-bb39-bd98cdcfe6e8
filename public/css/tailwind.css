/*! tailwindcss v4.1.4 | MIT License | https://tailwindcss.com */
@layer properties;
@layer theme, base, components, utilities;
@layer theme {
  :root, :host {
    --font-sans: ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji",
      "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
    --font-mono: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono",
      "Courier New", monospace;
    --color-red-400: oklch(70.4% 0.191 22.216);
    --color-red-500: oklch(63.7% 0.237 25.331);
    --color-orange-300: oklch(83.7% 0.128 66.29);
    --color-green-600: oklch(62.7% 0.194 149.214);
    --color-teal-100: oklch(95.3% 0.051 180.801);
    --color-teal-300: oklch(85.5% 0.138 181.071);
    --color-teal-400: oklch(77.7% 0.152 181.912);
    --color-teal-600: oklch(60% 0.118 184.704);
    --color-teal-900: oklch(38.6% 0.063 188.416);
    --color-cyan-400: oklch(78.9% 0.154 211.53);
    --color-sky-300: oklch(82.8% 0.111 230.318);
    --color-sky-500: oklch(68.5% 0.169 237.323);
    --color-blue-100: oklch(93.2% 0.032 255.585);
    --color-blue-300: oklch(80.9% 0.105 251.813);
    --color-blue-400: oklch(70.7% 0.165 254.624);
    --color-blue-500: oklch(62.3% 0.214 259.815);
    --color-blue-600: oklch(54.6% 0.245 262.881);
    --color-blue-700: oklch(48.8% 0.243 264.376);
    --color-blue-800: oklch(42.4% 0.199 265.638);
    --color-blue-900: oklch(37.9% 0.146 265.522);
    --color-indigo-100: oklch(93% 0.034 272.788);
    --color-indigo-400: oklch(67.3% 0.182 276.935);
    --color-indigo-500: oklch(58.5% 0.233 277.117);
    --color-indigo-600: oklch(51.1% 0.262 276.966);
    --color-indigo-900: oklch(35.9% 0.144 278.697);
    --color-purple-400: oklch(71.4% 0.203 305.504);
    --color-purple-700: oklch(49.6% 0.265 301.924);
    --color-gray-50: oklch(98.5% 0.002 247.839);
    --color-gray-100: oklch(96.7% 0.003 264.542);
    --color-gray-200: oklch(92.8% 0.006 264.531);
    --color-gray-300: oklch(87.2% 0.01 258.338);
    --color-gray-400: oklch(70.7% 0.022 261.325);
    --color-gray-500: oklch(55.1% 0.027 264.364);
    --color-gray-600: oklch(44.6% 0.03 256.802);
    --color-gray-700: oklch(37.3% 0.034 259.733);
    --color-gray-800: oklch(27.8% 0.033 256.848);
    --color-gray-900: oklch(21% 0.034 264.665);
    --color-gray-950: oklch(13% 0.028 261.692);
    --color-black: #000;
    --color-white: #fff;
    --spacing: 0.25rem;
    --container-3xs: 16rem;
    --container-md: 28rem;
    --container-lg: 32rem;
    --container-xl: 36rem;
    --container-2xl: 42rem;
    --container-3xl: 48rem;
    --container-7xl: 80rem;
    --text-xs: 0.75rem;
    --text-xs--line-height: calc(1 / 0.75);
    --text-sm: 0.875rem;
    --text-sm--line-height: calc(1.25 / 0.875);
    --text-base: 1rem;
    --text-base--line-height: calc(1.5 / 1);
    --text-lg: 1.125rem;
    --text-lg--line-height: calc(1.75 / 1.125);
    --text-xl: 1.25rem;
    --text-xl--line-height: calc(1.75 / 1.25);
    --text-2xl: 1.5rem;
    --text-2xl--line-height: calc(2 / 1.5);
    --text-3xl: 1.875rem;
    --text-3xl--line-height: calc(2.25 / 1.875);
    --text-4xl: 2.25rem;
    --text-4xl--line-height: calc(2.5 / 2.25);
    --text-5xl: 3rem;
    --text-5xl--line-height: 1;
    --text-6xl: 3.75rem;
    --text-6xl--line-height: 1;
    --text-7xl: 4.5rem;
    --text-7xl--line-height: 1;
    --font-weight-normal: 400;
    --font-weight-medium: 500;
    --font-weight-semibold: 600;
    --font-weight-bold: 700;
    --tracking-tight: -0.025em;
    --tracking-wide: 0.025em;
    --tracking-wider: 0.05em;
    --tracking-widest: 0.1em;
    --leading-tight: 1.25;
    --leading-snug: 1.375;
    --leading-normal: 1.5;
    --leading-loose: 2;
    --radius-sm: 0.25rem;
    --radius-md: 0.375rem;
    --radius-lg: 0.5rem;
    --radius-xl: 0.75rem;
    --radius-2xl: 1rem;
    --radius-3xl: 1.5rem;
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    --ease-in: cubic-bezier(0.4, 0, 1, 1);
    --ease-out: cubic-bezier(0, 0, 0.2, 1);
    --ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);
    --blur-2xl: 40px;
    --default-transition-duration: 150ms;
    --default-transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    --default-font-family: var(--font-sans);
    --default-mono-font-family: var(--font-mono);
    --color-primary: oklch(62.7% 0.194 149.214);
    --color-secondary: white;
  }
}
@layer base {
  *, ::after, ::before, ::backdrop, ::file-selector-button {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
    border: 0 solid;
  }
  html, :host {
    line-height: 1.5;
    -webkit-text-size-adjust: 100%;
    tab-size: 4;
    font-family: var(--default-font-family, ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji");
    font-feature-settings: var(--default-font-feature-settings, normal);
    font-variation-settings: var(--default-font-variation-settings, normal);
    -webkit-tap-highlight-color: transparent;
  }
  hr {
    height: 0;
    color: inherit;
    border-top-width: 1px;
  }
  abbr:where([title]) {
    -webkit-text-decoration: underline dotted;
    text-decoration: underline dotted;
  }
  h1, h2, h3, h4, h5, h6 {
    font-size: inherit;
    font-weight: inherit;
  }
  a {
    color: inherit;
    -webkit-text-decoration: inherit;
    text-decoration: inherit;
  }
  b, strong {
    font-weight: bolder;
  }
  code, kbd, samp, pre {
    font-family: var(--default-mono-font-family, ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace);
    font-feature-settings: var(--default-mono-font-feature-settings, normal);
    font-variation-settings: var(--default-mono-font-variation-settings, normal);
    font-size: 1em;
  }
  small {
    font-size: 80%;
  }
  sub, sup {
    font-size: 75%;
    line-height: 0;
    position: relative;
    vertical-align: baseline;
  }
  sub {
    bottom: -0.25em;
  }
  sup {
    top: -0.5em;
  }
  table {
    text-indent: 0;
    border-color: inherit;
    border-collapse: collapse;
  }
  :-moz-focusring {
    outline: auto;
  }
  progress {
    vertical-align: baseline;
  }
  summary {
    display: list-item;
  }
  ol, ul, menu {
    list-style: none;
  }
  img, svg, video, canvas, audio, iframe, embed, object {
    display: block;
    vertical-align: middle;
  }
  img, video {
    max-width: 100%;
    height: auto;
  }
  button, input, select, optgroup, textarea, ::file-selector-button {
    font: inherit;
    font-feature-settings: inherit;
    font-variation-settings: inherit;
    letter-spacing: inherit;
    color: inherit;
    border-radius: 0;
    background-color: transparent;
    opacity: 1;
  }
  :where(select:is([multiple], [size])) optgroup {
    font-weight: bolder;
  }
  :where(select:is([multiple], [size])) optgroup option {
    padding-inline-start: 20px;
  }
  ::file-selector-button {
    margin-inline-end: 4px;
  }
  ::placeholder {
    opacity: 1;
  }
  @supports (not (-webkit-appearance: -apple-pay-button))  or (contain-intrinsic-size: 1px) {
    ::placeholder {
      color: currentcolor;
      @supports (color: color-mix(in lab, red, red)) {
        color: color-mix(in oklab, currentcolor 50%, transparent);
      }
    }
  }
  textarea {
    resize: vertical;
  }
  ::-webkit-search-decoration {
    -webkit-appearance: none;
  }
  ::-webkit-date-and-time-value {
    min-height: 1lh;
    text-align: inherit;
  }
  ::-webkit-datetime-edit {
    display: inline-flex;
  }
  ::-webkit-datetime-edit-fields-wrapper {
    padding: 0;
  }
  ::-webkit-datetime-edit, ::-webkit-datetime-edit-year-field, ::-webkit-datetime-edit-month-field, ::-webkit-datetime-edit-day-field, ::-webkit-datetime-edit-hour-field, ::-webkit-datetime-edit-minute-field, ::-webkit-datetime-edit-second-field, ::-webkit-datetime-edit-millisecond-field, ::-webkit-datetime-edit-meridiem-field {
    padding-block: 0;
  }
  :-moz-ui-invalid {
    box-shadow: none;
  }
  button, input:where([type="button"], [type="reset"], [type="submit"]), ::file-selector-button {
    appearance: button;
  }
  ::-webkit-inner-spin-button, ::-webkit-outer-spin-button {
    height: auto;
  }
  [hidden]:where(:not([hidden="until-found"])) {
    display: none !important;
  }
}
@layer utilities {
  .\@container\/bard {
    container-type: inline-size;
    container-name: bard;
  }
  .\@container\/live-preview {
    container-type: inline-size;
    container-name: live-preview;
  }
  .\@container\/markdown {
    container-type: inline-size;
    container-name: markdown;
  }
  .\@container\/toolbar {
    container-type: inline-size;
    container-name: toolbar;
  }
  .\@container {
    container-type: inline-size;
  }
  .pointer-events-none {
    pointer-events: none;
  }
  .collapse {
    visibility: collapse;
  }
  .invisible {
    visibility: hidden;
  }
  .visible {
    visibility: visible;
  }
  .visible\! {
    visibility: visible !important;
  }
  .sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border-width: 0;
  }
  .visible {
    visibility: visible;
  }
  .absolute {
    position: absolute;
  }
  .fixed {
    position: fixed;
  }
  .relative {
    position: relative;
  }
  .static {
    position: static;
  }
  .sticky {
    position: sticky;
  }
  .inset-0 {
    inset: calc(var(--spacing) * 0);
  }
  .inset-x-0 {
    inset-inline: calc(var(--spacing) * 0);
  }
  .-top-1 {
    top: calc(var(--spacing) * -1);
  }
  .top-0 {
    top: calc(var(--spacing) * 0);
  }
  .top-1 {
    top: calc(var(--spacing) * 1);
  }
  .top-2 {
    top: calc(var(--spacing) * 2);
  }
  .top-3 {
    top: calc(var(--spacing) * 3);
  }
  .top-full {
    top: 100%;
  }
  .right-1 {
    right: calc(var(--spacing) * 1);
  }
  .right-3 {
    right: calc(var(--spacing) * 3);
  }
  .left-0 {
    left: calc(var(--spacing) * 0);
  }
  .isolate {
    isolation: isolate;
  }
  .z-1 {
    z-index: 1;
  }
  .z-10 {
    z-index: 10;
  }
  .z-20 {
    z-index: 20;
  }
  .z-200 {
    z-index: 200;
  }
  .container {
    width: 100%;
    @media (width >= 40rem) {
      max-width: 40rem;
    }
    @media (width >= 48rem) {
      max-width: 48rem;
    }
    @media (width >= 64rem) {
      max-width: 64rem;
    }
    @media (width >= 80rem) {
      max-width: 80rem;
    }
    @media (width >= 96rem) {
      max-width: 96rem;
    }
  }
  .-m-2 {
    margin: calc(var(--spacing) * -2);
  }
  .m-0 {
    margin: calc(var(--spacing) * 0);
  }
  .m-4 {
    margin: calc(var(--spacing) * 4);
  }
  .m-auto {
    margin: auto;
  }
  .-mx-1 {
    margin-inline: calc(var(--spacing) * -1);
  }
  .-mx-2 {
    margin-inline: calc(var(--spacing) * -2);
  }
  .-mx-4 {
    margin-inline: calc(var(--spacing) * -4);
  }
  .-mx-6 {
    margin-inline: calc(var(--spacing) * -6);
  }
  .mx-1 {
    margin-inline: calc(var(--spacing) * 1);
  }
  .mx-2 {
    margin-inline: calc(var(--spacing) * 2);
  }
  .mx-2\.5 {
    margin-inline: calc(var(--spacing) * 2.5);
  }
  .mx-4 {
    margin-inline: calc(var(--spacing) * 4);
  }
  .mx-auto {
    margin-inline: auto;
  }
  .-my-1 {
    margin-block: calc(var(--spacing) * -1);
  }
  .my-1 {
    margin-block: calc(var(--spacing) * 1);
  }
  .my-2 {
    margin-block: calc(var(--spacing) * 2);
  }
  .my-4 {
    margin-block: calc(var(--spacing) * 4);
  }
  .my-6 {
    margin-block: calc(var(--spacing) * 6);
  }
  .my-8 {
    margin-block: calc(var(--spacing) * 8);
  }
  .-mt-1 {
    margin-top: calc(var(--spacing) * -1);
  }
  .-mt-2 {
    margin-top: calc(var(--spacing) * -2);
  }
  .-mt-6 {
    margin-top: calc(var(--spacing) * -6);
  }
  .-mt-8 {
    margin-top: calc(var(--spacing) * -8);
  }
  .-mt-px {
    margin-top: -1px;
  }
  .mt-0 {
    margin-top: calc(var(--spacing) * 0);
  }
  .mt-0\.5 {
    margin-top: calc(var(--spacing) * 0.5);
  }
  .mt-1 {
    margin-top: calc(var(--spacing) * 1);
  }
  .mt-2 {
    margin-top: calc(var(--spacing) * 2);
  }
  .mt-3 {
    margin-top: calc(var(--spacing) * 3);
  }
  .mt-4 {
    margin-top: calc(var(--spacing) * 4);
  }
  .mt-5 {
    margin-top: calc(var(--spacing) * 5);
  }
  .mt-6 {
    margin-top: calc(var(--spacing) * 6);
  }
  .mt-8 {
    margin-top: calc(var(--spacing) * 8);
  }
  .mt-10 {
    margin-top: calc(var(--spacing) * 10);
  }
  .mt-12 {
    margin-top: calc(var(--spacing) * 12);
  }
  .mt-14 {
    margin-top: calc(var(--spacing) * 14);
  }
  .mt-16 {
    margin-top: calc(var(--spacing) * 16);
  }
  .mt-20 {
    margin-top: calc(var(--spacing) * 20);
  }
  .-mr-2 {
    margin-right: calc(var(--spacing) * -2);
  }
  .-mr-6 {
    margin-right: calc(var(--spacing) * -6);
  }
  .mr-2 {
    margin-right: calc(var(--spacing) * 2);
  }
  .mr-4 {
    margin-right: calc(var(--spacing) * 4);
  }
  .mr-8 {
    margin-right: calc(var(--spacing) * 8);
  }
  .-mb-2 {
    margin-bottom: calc(var(--spacing) * -2);
  }
  .-mb-3 {
    margin-bottom: calc(var(--spacing) * -3);
  }
  .mb-0 {
    margin-bottom: calc(var(--spacing) * 0);
  }
  .mb-1 {
    margin-bottom: calc(var(--spacing) * 1);
  }
  .mb-1\.5 {
    margin-bottom: calc(var(--spacing) * 1.5);
  }
  .mb-2 {
    margin-bottom: calc(var(--spacing) * 2);
  }
  .mb-3 {
    margin-bottom: calc(var(--spacing) * 3);
  }
  .mb-4 {
    margin-bottom: calc(var(--spacing) * 4);
  }
  .mb-5 {
    margin-bottom: calc(var(--spacing) * 5);
  }
  .mb-6 {
    margin-bottom: calc(var(--spacing) * 6);
  }
  .mb-8 {
    margin-bottom: calc(var(--spacing) * 8);
  }
  .mb-10 {
    margin-bottom: calc(var(--spacing) * 10);
  }
  .mb-12 {
    margin-bottom: calc(var(--spacing) * 12);
  }
  .mb-16 {
    margin-bottom: calc(var(--spacing) * 16);
  }
  .mb-20 {
    margin-bottom: calc(var(--spacing) * 20);
  }
  .mb-40 {
    margin-bottom: calc(var(--spacing) * 40);
  }
  .mb-px {
    margin-bottom: 1px;
  }
  .ml-0\.5 {
    margin-left: calc(var(--spacing) * 0.5);
  }
  .ml-2 {
    margin-left: calc(var(--spacing) * 2);
  }
  .ml-4 {
    margin-left: calc(var(--spacing) * 4);
  }
  .ml-auto {
    margin-left: auto;
  }
  .\!hidden {
    display: none !important;
  }
  .block {
    display: block;
  }
  .contents {
    display: contents;
  }
  .flex {
    display: flex;
  }
  .grid {
    display: grid;
  }
  .hidden {
    display: none;
  }
  .inline {
    display: inline;
  }
  .inline\! {
    display: inline !important;
  }
  .inline-block {
    display: inline-block;
  }
  .inline-flex {
    display: inline-flex;
  }
  .inline-grid {
    display: inline-grid;
  }
  .inline-table {
    display: inline-table;
  }
  .list-item {
    display: list-item;
  }
  .table {
    display: table;
  }
  .table-caption {
    display: table-caption;
  }
  .table-cell {
    display: table-cell;
  }
  .table-column {
    display: table-column;
  }
  .table-column-group {
    display: table-column-group;
  }
  .table-footer-group {
    display: table-footer-group;
  }
  .table-header-group {
    display: table-header-group;
  }
  .table-row {
    display: table-row;
  }
  .table-row-group {
    display: table-row-group;
  }
  .aspect-3\/2 {
    aspect-ratio: 3/2;
  }
  .aspect-auto {
    aspect-ratio: auto;
  }
  .size-4 {
    width: calc(var(--spacing) * 4);
    height: calc(var(--spacing) * 4);
  }
  .size-5 {
    width: calc(var(--spacing) * 5);
    height: calc(var(--spacing) * 5);
  }
  .h-0\.5 {
    height: calc(var(--spacing) * 0.5);
  }
  .h-1 {
    height: calc(var(--spacing) * 1);
  }
  .h-1\.5 {
    height: calc(var(--spacing) * 1.5);
  }
  .h-1\/2 {
    height: calc(1/2 * 100%);
  }
  .h-2 {
    height: calc(var(--spacing) * 2);
  }
  .h-3 {
    height: calc(var(--spacing) * 3);
  }
  .h-3\.5 {
    height: calc(var(--spacing) * 3.5);
  }
  .h-4 {
    height: calc(var(--spacing) * 4);
  }
  .h-5 {
    height: calc(var(--spacing) * 5);
  }
  .h-6 {
    height: calc(var(--spacing) * 6);
  }
  .h-7 {
    height: calc(var(--spacing) * 7);
  }
  .h-8 {
    height: calc(var(--spacing) * 8);
  }
  .h-9 {
    height: calc(var(--spacing) * 9);
  }
  .h-10 {
    height: calc(var(--spacing) * 10);
  }
  .h-11 {
    height: calc(var(--spacing) * 11);
  }
  .h-12 {
    height: calc(var(--spacing) * 12);
  }
  .h-13 {
    height: calc(var(--spacing) * 13);
  }
  .h-14 {
    height: calc(var(--spacing) * 14);
  }
  .h-16 {
    height: calc(var(--spacing) * 16);
  }
  .h-24 {
    height: calc(var(--spacing) * 24);
  }
  .h-32 {
    height: calc(var(--spacing) * 32);
  }
  .h-48 {
    height: calc(var(--spacing) * 48);
  }
  .h-56 {
    height: calc(var(--spacing) * 56);
  }
  .h-64 {
    height: calc(var(--spacing) * 64);
  }
  .h-auto {
    height: auto;
  }
  .h-full {
    height: 100%;
  }
  .h-max {
    height: max-content;
  }
  .h-screen {
    height: 100vh;
  }
  .max-h-5 {
    max-height: calc(var(--spacing) * 5);
  }
  .max-h-8 {
    max-height: calc(var(--spacing) * 8);
  }
  .max-h-10 {
    max-height: calc(var(--spacing) * 10);
  }
  .max-h-\[21rem\] {
    max-height: 21rem;
  }
  .max-h-\[75vh\] {
    max-height: 75vh;
  }
  .max-h-full {
    max-height: 100%;
  }
  .min-h-0 {
    min-height: calc(var(--spacing) * 0);
  }
  .min-h-20 {
    min-height: calc(var(--spacing) * 20);
  }
  .min-h-40 {
    min-height: calc(var(--spacing) * 40);
  }
  .min-h-\[45vh\] {
    min-height: 45vh;
  }
  .min-h-screen {
    min-height: 100vh;
  }
  .w-1 {
    width: calc(var(--spacing) * 1);
  }
  .w-1\/2 {
    width: calc(1/2 * 100%);
  }
  .w-1\/4 {
    width: calc(1/4 * 100%);
  }
  .w-2 {
    width: calc(var(--spacing) * 2);
  }
  .w-2\/3 {
    width: calc(2/3 * 100%);
  }
  .w-3 {
    width: calc(var(--spacing) * 3);
  }
  .w-3\.5 {
    width: calc(var(--spacing) * 3.5);
  }
  .w-4 {
    width: calc(var(--spacing) * 4);
  }
  .w-5 {
    width: calc(var(--spacing) * 5);
  }
  .w-5\/6 {
    width: calc(5/6 * 100%);
  }
  .w-6 {
    width: calc(var(--spacing) * 6);
  }
  .w-7 {
    width: calc(var(--spacing) * 7);
  }
  .w-8 {
    width: calc(var(--spacing) * 8);
  }
  .w-9 {
    width: calc(var(--spacing) * 9);
  }
  .w-10 {
    width: calc(var(--spacing) * 10);
  }
  .w-10\/12 {
    width: calc(10/12 * 100%);
  }
  .w-12 {
    width: calc(var(--spacing) * 12);
  }
  .w-14 {
    width: calc(var(--spacing) * 14);
  }
  .w-16 {
    width: calc(var(--spacing) * 16);
  }
  .w-24 {
    width: calc(var(--spacing) * 24);
  }
  .w-28 {
    width: calc(var(--spacing) * 28);
  }
  .w-32 {
    width: calc(var(--spacing) * 32);
  }
  .w-52 {
    width: calc(var(--spacing) * 52);
  }
  .w-64 {
    width: calc(var(--spacing) * 64);
  }
  .w-84 {
    width: calc(var(--spacing) * 84);
  }
  .w-96 {
    width: calc(var(--spacing) * 96);
  }
  .w-auto {
    width: auto;
  }
  .w-full {
    width: 100%;
  }
  .w-screen {
    width: 100vw;
  }
  .max-w-2xl {
    max-width: var(--container-2xl);
  }
  .max-w-3xl {
    max-width: var(--container-3xl);
  }
  .max-w-3xs {
    max-width: var(--container-3xs);
  }
  .max-w-5 {
    max-width: calc(var(--spacing) * 5);
  }
  .max-w-7xl {
    max-width: var(--container-7xl);
  }
  .max-w-\[130px\] {
    max-width: 130px;
  }
  .max-w-full {
    max-width: 100%;
  }
  .max-w-lg {
    max-width: var(--container-lg);
  }
  .max-w-md {
    max-width: var(--container-md);
  }
  .max-w-xl {
    max-width: var(--container-xl);
  }
  .min-w-0 {
    min-width: calc(var(--spacing) * 0);
  }
  .min-w-100 {
    min-width: calc(var(--spacing) * 100);
  }
  .min-w-\[18rem\] {
    min-width: 18rem;
  }
  .min-w-\[145px\] {
    min-width: 145px;
  }
  .min-w-\[240px\] {
    min-width: 240px;
  }
  .min-w-max {
    min-width: max-content;
  }
  .flex-1 {
    flex: 1;
  }
  .flex-initial {
    flex: 0 auto;
  }
  .flex-none {
    flex: none;
  }
  .flex-shrink {
    flex-shrink: 1;
  }
  .shrink {
    flex-shrink: 1;
  }
  .shrink-0 {
    flex-shrink: 0;
  }
  .flex-grow {
    flex-grow: 1;
  }
  .grow {
    flex-grow: 1;
  }
  .grow-1 {
    flex-grow: 1;
  }
  .table-fixed {
    table-layout: fixed;
  }
  .border-collapse {
    border-collapse: collapse;
  }
  .origin-bottom {
    transform-origin: bottom;
  }
  .origin-top {
    transform-origin: top;
  }
  .origin-top-right {
    transform-origin: top right;
  }
  .-translate-x-4 {
    --tw-translate-x: calc(var(--spacing) * -4);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }
  .translate-y-1 {
    --tw-translate-y: calc(var(--spacing) * 1);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }
  .scale-90 {
    --tw-scale-x: 90%;
    --tw-scale-y: 90%;
    --tw-scale-z: 90%;
    scale: var(--tw-scale-x) var(--tw-scale-y);
  }
  .scale-y-0 {
    --tw-scale-y: 0%;
    scale: var(--tw-scale-x) var(--tw-scale-y);
  }
  .-rotate-90 {
    rotate: calc(90deg * -1);
  }
  .rotate-90 {
    rotate: 90deg;
  }
  .rotate-180 {
    rotate: 180deg;
  }
  .transform {
    transform: var(--tw-rotate-x,) var(--tw-rotate-y,) var(--tw-rotate-z,) var(--tw-skew-x,) var(--tw-skew-y,);
  }
  .transform\! {
    transform: var(--tw-rotate-x,) var(--tw-rotate-y,) var(--tw-rotate-z,) var(--tw-skew-x,) var(--tw-skew-y,) !important;
  }
  .button {
    cursor: pointer;
  }
  .button\! {
    cursor: pointer !important;
  }
  .cursor-grab {
    cursor: grab;
  }
  .cursor-not-allowed {
    cursor: not-allowed;
  }
  .cursor-pointer {
    cursor: pointer;
  }
  .resize {
    resize: both;
  }
  .list-inside {
    list-style-position: inside;
  }
  .list-disc {
    list-style-type: disc;
  }
  .appearance-none {
    appearance: none;
  }
  .grid-cols-1 {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }
  .grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
  .grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }
  .grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }
  .flex-col {
    flex-direction: column;
  }
  .flex-col-reverse {
    flex-direction: column-reverse;
  }
  .flex-row-reverse {
    flex-direction: row-reverse;
  }
  .flex-wrap {
    flex-wrap: wrap;
  }
  .items-baseline {
    align-items: baseline;
  }
  .items-center {
    align-items: center;
  }
  .items-end {
    align-items: flex-end;
  }
  .items-start {
    align-items: flex-start;
  }
  .justify-between {
    justify-content: space-between;
  }
  .justify-center {
    justify-content: center;
  }
  .justify-end {
    justify-content: flex-end;
  }
  .justify-start {
    justify-content: flex-start;
  }
  .gap-2 {
    gap: calc(var(--spacing) * 2);
  }
  .gap-3 {
    gap: calc(var(--spacing) * 3);
  }
  .gap-4 {
    gap: calc(var(--spacing) * 4);
  }
  .gap-6 {
    gap: calc(var(--spacing) * 6);
  }
  .gap-8 {
    gap: calc(var(--spacing) * 8);
  }
  .space-y-1 {
    :where(& > :not(:last-child)) {
      --tw-space-y-reverse: 0;
      margin-block-start: calc(calc(var(--spacing) * 1) * var(--tw-space-y-reverse));
      margin-block-end: calc(calc(var(--spacing) * 1) * calc(1 - var(--tw-space-y-reverse)));
    }
  }
  .space-y-2 {
    :where(& > :not(:last-child)) {
      --tw-space-y-reverse: 0;
      margin-block-start: calc(calc(var(--spacing) * 2) * var(--tw-space-y-reverse));
      margin-block-end: calc(calc(var(--spacing) * 2) * calc(1 - var(--tw-space-y-reverse)));
    }
  }
  .space-y-4 {
    :where(& > :not(:last-child)) {
      --tw-space-y-reverse: 0;
      margin-block-start: calc(calc(var(--spacing) * 4) * var(--tw-space-y-reverse));
      margin-block-end: calc(calc(var(--spacing) * 4) * calc(1 - var(--tw-space-y-reverse)));
    }
  }
  .space-y-6 {
    :where(& > :not(:last-child)) {
      --tw-space-y-reverse: 0;
      margin-block-start: calc(calc(var(--spacing) * 6) * var(--tw-space-y-reverse));
      margin-block-end: calc(calc(var(--spacing) * 6) * calc(1 - var(--tw-space-y-reverse)));
    }
  }
  .space-y-8 {
    :where(& > :not(:last-child)) {
      --tw-space-y-reverse: 0;
      margin-block-start: calc(calc(var(--spacing) * 8) * var(--tw-space-y-reverse));
      margin-block-end: calc(calc(var(--spacing) * 8) * calc(1 - var(--tw-space-y-reverse)));
    }
  }
  .space-y-40 {
    :where(& > :not(:last-child)) {
      --tw-space-y-reverse: 0;
      margin-block-start: calc(calc(var(--spacing) * 40) * var(--tw-space-y-reverse));
      margin-block-end: calc(calc(var(--spacing) * 40) * calc(1 - var(--tw-space-y-reverse)));
    }
  }
  .gap-x-4 {
    column-gap: calc(var(--spacing) * 4);
  }
  .gap-x-6 {
    column-gap: calc(var(--spacing) * 6);
  }
  .gap-x-8 {
    column-gap: calc(var(--spacing) * 8);
  }
  .-space-x-2 {
    :where(& > :not(:last-child)) {
      --tw-space-x-reverse: 0;
      margin-inline-start: calc(calc(var(--spacing) * -2) * var(--tw-space-x-reverse));
      margin-inline-end: calc(calc(var(--spacing) * -2) * calc(1 - var(--tw-space-x-reverse)));
    }
  }
  .-space-x-52 {
    :where(& > :not(:last-child)) {
      --tw-space-x-reverse: 0;
      margin-inline-start: calc(calc(var(--spacing) * -52) * var(--tw-space-x-reverse));
      margin-inline-end: calc(calc(var(--spacing) * -52) * calc(1 - var(--tw-space-x-reverse)));
    }
  }
  .space-x-1 {
    :where(& > :not(:last-child)) {
      --tw-space-x-reverse: 0;
      margin-inline-start: calc(calc(var(--spacing) * 1) * var(--tw-space-x-reverse));
      margin-inline-end: calc(calc(var(--spacing) * 1) * calc(1 - var(--tw-space-x-reverse)));
    }
  }
  .space-x-2 {
    :where(& > :not(:last-child)) {
      --tw-space-x-reverse: 0;
      margin-inline-start: calc(calc(var(--spacing) * 2) * var(--tw-space-x-reverse));
      margin-inline-end: calc(calc(var(--spacing) * 2) * calc(1 - var(--tw-space-x-reverse)));
    }
  }
  .space-x-3 {
    :where(& > :not(:last-child)) {
      --tw-space-x-reverse: 0;
      margin-inline-start: calc(calc(var(--spacing) * 3) * var(--tw-space-x-reverse));
      margin-inline-end: calc(calc(var(--spacing) * 3) * calc(1 - var(--tw-space-x-reverse)));
    }
  }
  .space-x-12 {
    :where(& > :not(:last-child)) {
      --tw-space-x-reverse: 0;
      margin-inline-start: calc(calc(var(--spacing) * 12) * var(--tw-space-x-reverse));
      margin-inline-end: calc(calc(var(--spacing) * 12) * calc(1 - var(--tw-space-x-reverse)));
    }
  }
  .gap-y-2 {
    row-gap: calc(var(--spacing) * 2);
  }
  .gap-y-4 {
    row-gap: calc(var(--spacing) * 4);
  }
  .gap-y-16 {
    row-gap: calc(var(--spacing) * 16);
  }
  .divide-x {
    :where(& > :not(:last-child)) {
      --tw-divide-x-reverse: 0;
      border-inline-style: var(--tw-border-style);
      border-inline-start-width: calc(1px * var(--tw-divide-x-reverse));
      border-inline-end-width: calc(1px * calc(1 - var(--tw-divide-x-reverse)));
    }
  }
  .divide-y {
    :where(& > :not(:last-child)) {
      --tw-divide-y-reverse: 0;
      border-bottom-style: var(--tw-border-style);
      border-top-style: var(--tw-border-style);
      border-top-width: calc(1px * var(--tw-divide-y-reverse));
      border-bottom-width: calc(1px * calc(1 - var(--tw-divide-y-reverse)));
    }
  }
  .divide-gray-100 {
    :where(& > :not(:last-child)) {
      border-color: var(--color-gray-100);
    }
  }
  .self-end {
    align-self: flex-end;
  }
  .self-start {
    align-self: flex-start;
  }
  .truncate {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  .overflow-auto {
    overflow: auto;
  }
  .overflow-hidden {
    overflow: hidden;
  }
  .overflow-scroll {
    overflow: scroll;
  }
  .overflow-x-auto {
    overflow-x: auto;
  }
  .overflow-y-auto {
    overflow-y: auto;
  }
  .overflow-y-hidden {
    overflow-y: hidden;
  }
  .overflow-y-scroll {
    overflow-y: scroll;
  }
  .rounded {
    border-radius: 0.25rem;
  }
  .rounded-2xl {
    border-radius: var(--radius-2xl);
  }
  .rounded-3xl {
    border-radius: var(--radius-3xl);
  }
  .rounded-full {
    border-radius: calc(infinity * 1px);
  }
  .rounded-lg {
    border-radius: var(--radius-lg);
  }
  .rounded-md {
    border-radius: var(--radius-md);
  }
  .rounded-none {
    border-radius: 0;
  }
  .rounded-sm {
    border-radius: var(--radius-sm);
  }
  .rounded-xl {
    border-radius: var(--radius-xl);
  }
  .rounded-t {
    border-top-left-radius: 0.25rem;
    border-top-right-radius: 0.25rem;
  }
  .rounded-t-lg {
    border-top-left-radius: var(--radius-lg);
    border-top-right-radius: var(--radius-lg);
  }
  .rounded-t-md {
    border-top-left-radius: var(--radius-md);
    border-top-right-radius: var(--radius-md);
  }
  .rounded-t-none {
    border-top-left-radius: 0;
    border-top-right-radius: 0;
  }
  .rounded-tl {
    border-top-left-radius: 0.25rem;
  }
  .rounded-b {
    border-bottom-right-radius: 0.25rem;
    border-bottom-left-radius: 0.25rem;
  }
  .rounded-b-lg {
    border-bottom-right-radius: var(--radius-lg);
    border-bottom-left-radius: var(--radius-lg);
  }
  .rounded-b-md {
    border-bottom-right-radius: var(--radius-md);
    border-bottom-left-radius: var(--radius-md);
  }
  .border {
    border-style: var(--tw-border-style);
    border-width: 1px;
  }
  .border-0 {
    border-style: var(--tw-border-style);
    border-width: 0px;
  }
  .border-2 {
    border-style: var(--tw-border-style);
    border-width: 2px;
  }
  .border-y {
    border-block-style: var(--tw-border-style);
    border-block-width: 1px;
  }
  .border-t {
    border-top-style: var(--tw-border-style);
    border-top-width: 1px;
  }
  .border-r {
    border-right-style: var(--tw-border-style);
    border-right-width: 1px;
  }
  .border-b {
    border-bottom-style: var(--tw-border-style);
    border-bottom-width: 1px;
  }
  .border-b-2 {
    border-bottom-style: var(--tw-border-style);
    border-bottom-width: 2px;
  }
  .border-l {
    border-left-style: var(--tw-border-style);
    border-left-width: 1px;
  }
  .border-dashed {
    --tw-border-style: dashed;
    border-style: dashed;
  }
  .border-black\/5 {
    border-color: color-mix(in srgb, #000 5%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      border-color: color-mix(in oklab, var(--color-black) 5%, transparent);
    }
  }
  .border-blue-400 {
    border-color: var(--color-blue-400);
  }
  .border-gray-100 {
    border-color: var(--color-gray-100);
  }
  .border-gray-300 {
    border-color: var(--color-gray-300);
  }
  .border-gray-400 {
    border-color: var(--color-gray-400);
  }
  .border-gray-500 {
    border-color: var(--color-gray-500);
  }
  .border-gray-600 {
    border-color: var(--color-gray-600);
  }
  .border-gray-900 {
    border-color: var(--color-gray-900);
  }
  .border-white {
    border-color: var(--color-white);
  }
  .bg-black\/10 {
    background-color: color-mix(in srgb, #000 10%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      background-color: color-mix(in oklab, var(--color-black) 10%, transparent);
    }
  }
  .bg-blue-100 {
    background-color: var(--color-blue-100);
  }
  .bg-gray-50 {
    background-color: var(--color-gray-50);
  }
  .bg-gray-100 {
    background-color: var(--color-gray-100);
  }
  .bg-gray-200 {
    background-color: var(--color-gray-200);
  }
  .bg-gray-300 {
    background-color: var(--color-gray-300);
  }
  .bg-gray-400 {
    background-color: var(--color-gray-400);
  }
  .bg-gray-500 {
    background-color: var(--color-gray-500);
  }
  .bg-gray-600 {
    background-color: var(--color-gray-600);
  }
  .bg-gray-800 {
    background-color: var(--color-gray-800);
  }
  .bg-gray-900 {
    background-color: var(--color-gray-900);
  }
  .bg-gray-950 {
    background-color: var(--color-gray-950);
  }
  .bg-green-600 {
    background-color: var(--color-green-600);
  }
  .bg-indigo-100 {
    background-color: var(--color-indigo-100);
  }
  .bg-primary {
    background-color: var(--color-primary);
  }
  .bg-primary\/20 {
    background-color: color-mix(in srgb, oklch(62.7% 0.194 149.214) 20%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      background-color: color-mix(in oklab, var(--color-primary) 20%, transparent);
    }
  }
  .bg-red-500 {
    background-color: var(--color-red-500);
  }
  .bg-teal-100 {
    background-color: var(--color-teal-100);
  }
  .bg-transparent {
    background-color: transparent;
  }
  .bg-white {
    background-color: var(--color-white);
  }
  .bg-white\/70 {
    background-color: color-mix(in srgb, #fff 70%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      background-color: color-mix(in oklab, var(--color-white) 70%, transparent);
    }
  }
  .bg-gradient-to-br {
    --tw-gradient-position: to bottom right in oklab;
    background-image: linear-gradient(var(--tw-gradient-stops));
  }
  .bg-gradient-to-r {
    --tw-gradient-position: to right in oklab;
    background-image: linear-gradient(var(--tw-gradient-stops));
  }
  .from-cyan-400 {
    --tw-gradient-from: var(--color-cyan-400);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .from-primary {
    --tw-gradient-from: var(--color-primary);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .to-purple-400 {
    --tw-gradient-to: var(--color-purple-400);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .to-sky-300 {
    --tw-gradient-to: var(--color-sky-300);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .bg-cover {
    background-size: cover;
  }
  .bg-center {
    background-position: center;
  }
  .bg-no-repeat {
    background-repeat: no-repeat;
  }
  .mask-repeat {
    mask-repeat: repeat;
  }
  .fill-current {
    fill: currentcolor;
  }
  .object-cover {
    object-fit: cover;
  }
  .object-top {
    object-position: top;
  }
  .p-0 {
    padding: calc(var(--spacing) * 0);
  }
  .p-1 {
    padding: calc(var(--spacing) * 1);
  }
  .p-2 {
    padding: calc(var(--spacing) * 2);
  }
  .p-3 {
    padding: calc(var(--spacing) * 3);
  }
  .p-4 {
    padding: calc(var(--spacing) * 4);
  }
  .p-5 {
    padding: calc(var(--spacing) * 5);
  }
  .p-6 {
    padding: calc(var(--spacing) * 6);
  }
  .p-8 {
    padding: calc(var(--spacing) * 8);
  }
  .p-\[2px\] {
    padding: 2px;
  }
  .p-px {
    padding: 1px;
  }
  .px-1 {
    padding-inline: calc(var(--spacing) * 1);
  }
  .px-2 {
    padding-inline: calc(var(--spacing) * 2);
  }
  .px-3 {
    padding-inline: calc(var(--spacing) * 3);
  }
  .px-4 {
    padding-inline: calc(var(--spacing) * 4);
  }
  .px-5 {
    padding-inline: calc(var(--spacing) * 5);
  }
  .px-6 {
    padding-inline: calc(var(--spacing) * 6);
  }
  .px-8 {
    padding-inline: calc(var(--spacing) * 8);
  }
  .px-px {
    padding-inline: 1px;
  }
  .py-0 {
    padding-block: calc(var(--spacing) * 0);
  }
  .py-0\.5 {
    padding-block: calc(var(--spacing) * 0.5);
  }
  .py-1 {
    padding-block: calc(var(--spacing) * 1);
  }
  .py-1\.5 {
    padding-block: calc(var(--spacing) * 1.5);
  }
  .py-2 {
    padding-block: calc(var(--spacing) * 2);
  }
  .py-2\.5 {
    padding-block: calc(var(--spacing) * 2.5);
  }
  .py-3 {
    padding-block: calc(var(--spacing) * 3);
  }
  .py-4 {
    padding-block: calc(var(--spacing) * 4);
  }
  .py-6 {
    padding-block: calc(var(--spacing) * 6);
  }
  .py-8 {
    padding-block: calc(var(--spacing) * 8);
  }
  .py-10 {
    padding-block: calc(var(--spacing) * 10);
  }
  .py-12 {
    padding-block: calc(var(--spacing) * 12);
  }
  .py-16 {
    padding-block: calc(var(--spacing) * 16);
  }
  .py-20 {
    padding-block: calc(var(--spacing) * 20);
  }
  .py-24 {
    padding-block: calc(var(--spacing) * 24);
  }
  .py-32 {
    padding-block: calc(var(--spacing) * 32);
  }
  .py-px {
    padding-block: 1px;
  }
  .pt-0 {
    padding-top: calc(var(--spacing) * 0);
  }
  .pt-1 {
    padding-top: calc(var(--spacing) * 1);
  }
  .pt-2 {
    padding-top: calc(var(--spacing) * 2);
  }
  .pt-4 {
    padding-top: calc(var(--spacing) * 4);
  }
  .pt-6 {
    padding-top: calc(var(--spacing) * 6);
  }
  .pt-16 {
    padding-top: calc(var(--spacing) * 16);
  }
  .pt-36 {
    padding-top: calc(var(--spacing) * 36);
  }
  .pt-px {
    padding-top: 1px;
  }
  .pb-0 {
    padding-bottom: calc(var(--spacing) * 0);
  }
  .pb-2 {
    padding-bottom: calc(var(--spacing) * 2);
  }
  .pb-4 {
    padding-bottom: calc(var(--spacing) * 4);
  }
  .pb-10 {
    padding-bottom: calc(var(--spacing) * 10);
  }
  .pb-20 {
    padding-bottom: calc(var(--spacing) * 20);
  }
  .pb-px {
    padding-bottom: 1px;
  }
  .text-center {
    text-align: center;
  }
  .text-justify {
    text-align: justify;
  }
  .text-left {
    text-align: left;
  }
  .align-middle {
    vertical-align: middle;
  }
  .font-mono {
    font-family: var(--font-mono);
  }
  .text-2xl {
    font-size: var(--text-2xl);
    line-height: var(--tw-leading, var(--text-2xl--line-height));
  }
  .text-3xl {
    font-size: var(--text-3xl);
    line-height: var(--tw-leading, var(--text-3xl--line-height));
  }
  .text-4xl {
    font-size: var(--text-4xl);
    line-height: var(--tw-leading, var(--text-4xl--line-height));
  }
  .text-base {
    font-size: var(--text-base);
    line-height: var(--tw-leading, var(--text-base--line-height));
  }
  .text-base\/7 {
    font-size: var(--text-base);
    line-height: calc(var(--spacing) * 7);
  }
  .text-lg {
    font-size: var(--text-lg);
    line-height: var(--tw-leading, var(--text-lg--line-height));
  }
  .text-lg\/8 {
    font-size: var(--text-lg);
    line-height: calc(var(--spacing) * 8);
  }
  .text-sm {
    font-size: var(--text-sm);
    line-height: var(--tw-leading, var(--text-sm--line-height));
  }
  .text-xl {
    font-size: var(--text-xl);
    line-height: var(--tw-leading, var(--text-xl--line-height));
  }
  .text-xs {
    font-size: var(--text-xs);
    line-height: var(--tw-leading, var(--text-xs--line-height));
  }
  .leading-6 {
    --tw-leading: calc(var(--spacing) * 6);
    line-height: calc(var(--spacing) * 6);
  }
  .leading-loose {
    --tw-leading: var(--leading-loose);
    line-height: var(--leading-loose);
  }
  .leading-none {
    --tw-leading: 1;
    line-height: 1;
  }
  .leading-normal {
    --tw-leading: var(--leading-normal);
    line-height: var(--leading-normal);
  }
  .leading-snug {
    --tw-leading: var(--leading-snug);
    line-height: var(--leading-snug);
  }
  .font-bold {
    --tw-font-weight: var(--font-weight-bold);
    font-weight: var(--font-weight-bold);
  }
  .font-medium {
    --tw-font-weight: var(--font-weight-medium);
    font-weight: var(--font-weight-medium);
  }
  .font-normal {
    --tw-font-weight: var(--font-weight-normal);
    font-weight: var(--font-weight-normal);
  }
  .font-semibold {
    --tw-font-weight: var(--font-weight-semibold);
    font-weight: var(--font-weight-semibold);
  }
  .tracking-wide {
    --tw-tracking: var(--tracking-wide);
    letter-spacing: var(--tracking-wide);
  }
  .tracking-wider {
    --tw-tracking: var(--tracking-wider);
    letter-spacing: var(--tracking-wider);
  }
  .tracking-widest {
    --tw-tracking: var(--tracking-widest);
    letter-spacing: var(--tracking-widest);
  }
  .text-wrap {
    text-wrap: wrap;
  }
  .break-all {
    word-break: break-all;
  }
  .text-ellipsis {
    text-overflow: ellipsis;
  }
  .whitespace-normal {
    white-space: normal;
  }
  .whitespace-nowrap {
    white-space: nowrap;
  }
  .whitespace-pre-wrap {
    white-space: pre-wrap;
  }
  .text-blue-400 {
    color: var(--color-blue-400);
  }
  .text-current {
    color: currentcolor;
  }
  .text-gray-400 {
    color: var(--color-gray-400);
  }
  .text-gray-500 {
    color: var(--color-gray-500);
  }
  .text-gray-600 {
    color: var(--color-gray-600);
  }
  .text-gray-700 {
    color: var(--color-gray-700);
  }
  .text-gray-800 {
    color: var(--color-gray-800);
  }
  .text-gray-900 {
    color: var(--color-gray-900);
  }
  .text-gray-950 {
    color: var(--color-gray-950);
  }
  .text-green-600 {
    color: var(--color-green-600);
  }
  .text-indigo-500 {
    color: var(--color-indigo-500);
  }
  .text-primary {
    color: var(--color-primary);
  }
  .text-purple-700 {
    color: var(--color-purple-700);
  }
  .text-red-500 {
    color: var(--color-red-500);
  }
  .text-secondary {
    color: var(--color-secondary);
  }
  .text-sky-500 {
    color: var(--color-sky-500);
  }
  .text-teal-600 {
    color: var(--color-teal-600);
  }
  .text-white {
    color: var(--color-white);
  }
  .capitalize {
    text-transform: capitalize;
  }
  .lowercase {
    text-transform: lowercase;
  }
  .uppercase {
    text-transform: uppercase;
  }
  .italic {
    font-style: italic;
  }
  .ordinal {
    --tw-ordinal: ordinal;
    font-variant-numeric: var(--tw-ordinal,) var(--tw-slashed-zero,) var(--tw-numeric-figure,) var(--tw-numeric-spacing,) var(--tw-numeric-fraction,);
  }
  .normal-nums {
    font-variant-numeric: normal;
  }
  .line-through {
    text-decoration-line: line-through;
  }
  .overline {
    text-decoration-line: overline;
  }
  .underline {
    text-decoration-line: underline;
  }
  .antialiased {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }
  .subpixel-antialiased {
    -webkit-font-smoothing: auto;
    -moz-osx-font-smoothing: auto;
  }
  .placeholder-gray-50 {
    &::placeholder {
      color: var(--color-gray-50);
    }
  }
  .opacity-0 {
    opacity: 0%;
  }
  .opacity-25 {
    opacity: 25%;
  }
  .opacity-40 {
    opacity: 40%;
  }
  .opacity-50 {
    opacity: 50%;
  }
  .opacity-100 {
    opacity: 100%;
  }
  .shadow {
    --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 1px 2px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
  .shadow-2xl {
    --tw-shadow: 0 25px 50px -12px var(--tw-shadow-color, rgb(0 0 0 / 0.25));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
  .shadow-\[inset_0px_4px_3px_0px_black\] {
    --tw-shadow: inset 0px 4px 3px 0px var(--tw-shadow-color, black);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
  .shadow-inner {
    --tw-shadow: inset 0 2px 4px 0 var(--tw-shadow-color, rgb(0 0 0 / 0.05));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
  .shadow-lg {
    --tw-shadow: 0 10px 15px -3px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 4px 6px -4px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
  .shadow-md {
    --tw-shadow: 0 4px 6px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 2px 4px -2px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
  .shadow-none {
    --tw-shadow: 0 0 #0000;
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
  .shadow-sm {
    --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 1px 2px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
  .shadow-gray-600\/10 {
    --tw-shadow-color: color-mix(in srgb, oklch(44.6% 0.03 256.802) 10%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      --tw-shadow-color: color-mix(in oklab, color-mix(in oklab, var(--color-gray-600) 10%, transparent) var(--tw-shadow-alpha), transparent);
    }
  }
  .outline {
    outline-style: var(--tw-outline-style);
    outline-width: 1px;
  }
  .outline-0 {
    outline-style: var(--tw-outline-style);
    outline-width: 0px;
  }
  .blur {
    --tw-blur: blur(8px);
    filter: var(--tw-blur,) var(--tw-brightness,) var(--tw-contrast,) var(--tw-grayscale,) var(--tw-hue-rotate,) var(--tw-invert,) var(--tw-saturate,) var(--tw-sepia,) var(--tw-drop-shadow,);
  }
  .blur-\[106px\] {
    --tw-blur: blur(106px);
    filter: var(--tw-blur,) var(--tw-brightness,) var(--tw-contrast,) var(--tw-grayscale,) var(--tw-hue-rotate,) var(--tw-invert,) var(--tw-saturate,) var(--tw-sepia,) var(--tw-drop-shadow,);
  }
  .drop-shadow {
    --tw-drop-shadow-size: drop-shadow(0 1px 2px var(--tw-drop-shadow-color, rgb(0 0 0 / 0.1))) drop-shadow(0 1px 1px var(--tw-drop-shadow-color, rgb(0 0 0 / 0.06)));
    --tw-drop-shadow: drop-shadow(0 1px 2px rgb(0 0 0 / 0.1)) drop-shadow( 0 1px 1px rgb(0 0 0 / 0.06));
    filter: var(--tw-blur,) var(--tw-brightness,) var(--tw-contrast,) var(--tw-grayscale,) var(--tw-hue-rotate,) var(--tw-invert,) var(--tw-saturate,) var(--tw-sepia,) var(--tw-drop-shadow,);
  }
  .grayscale {
    --tw-grayscale: grayscale(100%);
    filter: var(--tw-blur,) var(--tw-brightness,) var(--tw-contrast,) var(--tw-grayscale,) var(--tw-hue-rotate,) var(--tw-invert,) var(--tw-saturate,) var(--tw-sepia,) var(--tw-drop-shadow,);
  }
  .invert {
    --tw-invert: invert(100%);
    filter: var(--tw-blur,) var(--tw-brightness,) var(--tw-contrast,) var(--tw-grayscale,) var(--tw-hue-rotate,) var(--tw-invert,) var(--tw-saturate,) var(--tw-sepia,) var(--tw-drop-shadow,);
  }
  .sepia {
    --tw-sepia: sepia(100%);
    filter: var(--tw-blur,) var(--tw-brightness,) var(--tw-contrast,) var(--tw-grayscale,) var(--tw-hue-rotate,) var(--tw-invert,) var(--tw-saturate,) var(--tw-sepia,) var(--tw-drop-shadow,);
  }
  .filter {
    filter: var(--tw-blur,) var(--tw-brightness,) var(--tw-contrast,) var(--tw-grayscale,) var(--tw-hue-rotate,) var(--tw-invert,) var(--tw-saturate,) var(--tw-sepia,) var(--tw-drop-shadow,);
  }
  .filter\! {
    filter: var(--tw-blur,) var(--tw-brightness,) var(--tw-contrast,) var(--tw-grayscale,) var(--tw-hue-rotate,) var(--tw-invert,) var(--tw-saturate,) var(--tw-sepia,) var(--tw-drop-shadow,) !important;
  }
  .backdrop-blur-2xl {
    --tw-backdrop-blur: blur(var(--blur-2xl));
    -webkit-backdrop-filter: var(--tw-backdrop-blur,) var(--tw-backdrop-brightness,) var(--tw-backdrop-contrast,) var(--tw-backdrop-grayscale,) var(--tw-backdrop-hue-rotate,) var(--tw-backdrop-invert,) var(--tw-backdrop-opacity,) var(--tw-backdrop-saturate,) var(--tw-backdrop-sepia,);
    backdrop-filter: var(--tw-backdrop-blur,) var(--tw-backdrop-brightness,) var(--tw-backdrop-contrast,) var(--tw-backdrop-grayscale,) var(--tw-backdrop-hue-rotate,) var(--tw-backdrop-invert,) var(--tw-backdrop-opacity,) var(--tw-backdrop-saturate,) var(--tw-backdrop-sepia,);
  }
  .backdrop-filter {
    -webkit-backdrop-filter: var(--tw-backdrop-blur,) var(--tw-backdrop-brightness,) var(--tw-backdrop-contrast,) var(--tw-backdrop-grayscale,) var(--tw-backdrop-hue-rotate,) var(--tw-backdrop-invert,) var(--tw-backdrop-opacity,) var(--tw-backdrop-saturate,) var(--tw-backdrop-sepia,);
    backdrop-filter: var(--tw-backdrop-blur,) var(--tw-backdrop-brightness,) var(--tw-backdrop-contrast,) var(--tw-backdrop-grayscale,) var(--tw-backdrop-hue-rotate,) var(--tw-backdrop-invert,) var(--tw-backdrop-opacity,) var(--tw-backdrop-saturate,) var(--tw-backdrop-sepia,);
  }
  .transition {
    transition-property: color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to, opacity, box-shadow, transform, translate, scale, rotate, filter, -webkit-backdrop-filter, backdrop-filter;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }
  .transition-all {
    transition-property: all;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }
  .duration-100 {
    --tw-duration: 100ms;
    transition-duration: 100ms;
  }
  .duration-150 {
    --tw-duration: 150ms;
    transition-duration: 150ms;
  }
  .duration-200 {
    --tw-duration: 200ms;
    transition-duration: 200ms;
  }
  .duration-300 {
    --tw-duration: 300ms;
    transition-duration: 300ms;
  }
  .duration-500 {
    --tw-duration: 500ms;
    transition-duration: 500ms;
  }
  .ease-in {
    --tw-ease: var(--ease-in);
    transition-timing-function: var(--ease-in);
  }
  .ease-in-out {
    --tw-ease: var(--ease-in-out);
    transition-timing-function: var(--ease-in-out);
  }
  .ease-out {
    --tw-ease: var(--ease-out);
    transition-timing-function: var(--ease-out);
  }
  .outline-none {
    --tw-outline-style: none;
    outline-style: none;
  }
  .select-none {
    -webkit-user-select: none;
    user-select: none;
  }
  .group-hover\:translate-x-0 {
    &:is(:where(.group):hover *) {
      @media (hover: hover) {
        --tw-translate-x: calc(var(--spacing) * 0);
        translate: var(--tw-translate-x) var(--tw-translate-y);
      }
    }
  }
  .group-hover\:scale-105 {
    &:is(:where(.group):hover *) {
      @media (hover: hover) {
        --tw-scale-x: 105%;
        --tw-scale-y: 105%;
        --tw-scale-z: 105%;
        scale: var(--tw-scale-x) var(--tw-scale-y);
      }
    }
  }
  .group-hover\:border-gray-600 {
    &:is(:where(.group):hover *) {
      @media (hover: hover) {
        border-color: var(--color-gray-600);
      }
    }
  }
  .group-hover\:bg-white {
    &:is(:where(.group):hover *) {
      @media (hover: hover) {
        background-color: var(--color-white);
      }
    }
  }
  .group-hover\:text-black {
    &:is(:where(.group):hover *) {
      @media (hover: hover) {
        color: var(--color-black);
      }
    }
  }
  .group-hover\:text-blue-500 {
    &:is(:where(.group):hover *) {
      @media (hover: hover) {
        color: var(--color-blue-500);
      }
    }
  }
  .group-hover\:text-blue-600 {
    &:is(:where(.group):hover *) {
      @media (hover: hover) {
        color: var(--color-blue-600);
      }
    }
  }
  .group-hover\:text-gray-800 {
    &:is(:where(.group):hover *) {
      @media (hover: hover) {
        color: var(--color-gray-800);
      }
    }
  }
  .group-hover\:text-gray-900 {
    &:is(:where(.group):hover *) {
      @media (hover: hover) {
        color: var(--color-gray-900);
      }
    }
  }
  .group-hover\:text-red-500 {
    &:is(:where(.group):hover *) {
      @media (hover: hover) {
        color: var(--color-red-500);
      }
    }
  }
  .group-hover\:text-secondary {
    &:is(:where(.group):hover *) {
      @media (hover: hover) {
        color: var(--color-secondary);
      }
    }
  }
  .group-hover\:text-white {
    &:is(:where(.group):hover *) {
      @media (hover: hover) {
        color: var(--color-white);
      }
    }
  }
  .group-hover\:opacity-100 {
    &:is(:where(.group):hover *) {
      @media (hover: hover) {
        opacity: 100%;
      }
    }
  }
  .group-data-\[state\=active\]\:visible {
    &:is(:where(.group)[data-state="active"] *) {
      visibility: visible;
    }
  }
  .group-data-\[state\=active\]\:origin-top {
    &:is(:where(.group)[data-state="active"] *) {
      transform-origin: top;
    }
  }
  .group-data-\[state\=active\]\:-translate-y-1 {
    &:is(:where(.group)[data-state="active"] *) {
      --tw-translate-y: calc(var(--spacing) * -1);
      translate: var(--tw-translate-x) var(--tw-translate-y);
    }
  }
  .group-data-\[state\=active\]\:translate-y-1\.5 {
    &:is(:where(.group)[data-state="active"] *) {
      --tw-translate-y: calc(var(--spacing) * 1.5);
      translate: var(--tw-translate-x) var(--tw-translate-y);
    }
  }
  .group-data-\[state\=active\]\:scale-100 {
    &:is(:where(.group)[data-state="active"] *) {
      --tw-scale-x: 100%;
      --tw-scale-y: 100%;
      --tw-scale-z: 100%;
      scale: var(--tw-scale-x) var(--tw-scale-y);
    }
  }
  .group-data-\[state\=active\]\:scale-y-100 {
    &:is(:where(.group)[data-state="active"] *) {
      --tw-scale-y: 100%;
      scale: var(--tw-scale-x) var(--tw-scale-y);
    }
  }
  .group-data-\[state\=active\]\:-rotate-45 {
    &:is(:where(.group)[data-state="active"] *) {
      rotate: calc(45deg * -1);
    }
  }
  .group-data-\[state\=active\]\:rotate-45 {
    &:is(:where(.group)[data-state="active"] *) {
      rotate: 45deg;
    }
  }
  .group-data-\[state\=active\]\:opacity-100 {
    &:is(:where(.group)[data-state="active"] *) {
      opacity: 100%;
    }
  }
  .placeholder\:text-gray-600 {
    &::placeholder {
      color: var(--color-gray-600);
    }
  }
  .before\:absolute {
    &::before {
      content: var(--tw-content);
      position: absolute;
    }
  }
  .before\:inset-0 {
    &::before {
      content: var(--tw-content);
      inset: calc(var(--spacing) * 0);
    }
  }
  .before\:rounded-full {
    &::before {
      content: var(--tw-content);
      border-radius: calc(infinity * 1px);
    }
  }
  .before\:border {
    &::before {
      content: var(--tw-content);
      border-style: var(--tw-border-style);
      border-width: 1px;
    }
  }
  .before\:border-transparent {
    &::before {
      content: var(--tw-content);
      border-color: transparent;
    }
  }
  .before\:bg-primary {
    &::before {
      content: var(--tw-content);
      background-color: var(--color-primary);
    }
  }
  .before\:bg-primary\/10 {
    &::before {
      content: var(--tw-content);
      background-color: color-mix(in srgb, oklch(62.7% 0.194 149.214) 10%, transparent);
      @supports (color: color-mix(in lab, red, red)) {
        background-color: color-mix(in oklab, var(--color-primary) 10%, transparent);
      }
    }
  }
  .before\:bg-gradient-to-b {
    &::before {
      content: var(--tw-content);
      --tw-gradient-position: to bottom in oklab;
      background-image: linear-gradient(var(--tw-gradient-stops));
    }
  }
  .before\:transition {
    &::before {
      content: var(--tw-content);
      transition-property: color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to, opacity, box-shadow, transform, translate, scale, rotate, filter, -webkit-backdrop-filter, backdrop-filter;
      transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
      transition-duration: var(--tw-duration, var(--default-transition-duration));
    }
  }
  .before\:duration-300 {
    &::before {
      content: var(--tw-content);
      --tw-duration: 300ms;
      transition-duration: 300ms;
    }
  }
  .hover\:z-\[1\] {
    &:hover {
      @media (hover: hover) {
        z-index: 1;
      }
    }
  }
  .hover\:border-gray-600 {
    &:hover {
      @media (hover: hover) {
        border-color: var(--color-gray-600);
      }
    }
  }
  .hover\:bg-gray-100 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-gray-100);
      }
    }
  }
  .hover\:bg-gray-200 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-gray-200);
      }
    }
  }
  .hover\:bg-gray-900 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-gray-900);
      }
    }
  }
  .hover\:text-black {
    &:hover {
      @media (hover: hover) {
        color: var(--color-black);
      }
    }
  }
  .hover\:text-blue-500 {
    &:hover {
      @media (hover: hover) {
        color: var(--color-blue-500);
      }
    }
  }
  .hover\:text-blue-800 {
    &:hover {
      @media (hover: hover) {
        color: var(--color-blue-800);
      }
    }
  }
  .hover\:text-gray-500 {
    &:hover {
      @media (hover: hover) {
        color: var(--color-gray-500);
      }
    }
  }
  .hover\:text-gray-700 {
    &:hover {
      @media (hover: hover) {
        color: var(--color-gray-700);
      }
    }
  }
  .hover\:text-gray-800 {
    &:hover {
      @media (hover: hover) {
        color: var(--color-gray-800);
      }
    }
  }
  .hover\:text-gray-900 {
    &:hover {
      @media (hover: hover) {
        color: var(--color-gray-900);
      }
    }
  }
  .hover\:text-gray-950 {
    &:hover {
      @media (hover: hover) {
        color: var(--color-gray-950);
      }
    }
  }
  .hover\:text-primary {
    &:hover {
      @media (hover: hover) {
        color: var(--color-primary);
      }
    }
  }
  .hover\:text-red-400 {
    &:hover {
      @media (hover: hover) {
        color: var(--color-red-400);
      }
    }
  }
  .hover\:opacity-75 {
    &:hover {
      @media (hover: hover) {
        opacity: 75%;
      }
    }
  }
  .hover\:opacity-100 {
    &:hover {
      @media (hover: hover) {
        opacity: 100%;
      }
    }
  }
  .hover\:shadow-2xl {
    &:hover {
      @media (hover: hover) {
        --tw-shadow: 0 25px 50px -12px var(--tw-shadow-color, rgb(0 0 0 / 0.25));
        box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
      }
    }
  }
  .hover\:shadow-md {
    &:hover {
      @media (hover: hover) {
        --tw-shadow: 0 4px 6px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 2px 4px -2px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
        box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
      }
    }
  }
  .hover\:shadow-gray-600\/10 {
    &:hover {
      @media (hover: hover) {
        --tw-shadow-color: color-mix(in srgb, oklch(44.6% 0.03 256.802) 10%, transparent);
        @supports (color: color-mix(in lab, red, red)) {
          --tw-shadow-color: color-mix(in oklab, color-mix(in oklab, var(--color-gray-600) 10%, transparent) var(--tw-shadow-alpha), transparent);
        }
      }
    }
  }
  .hover\:grayscale-0 {
    &:hover {
      @media (hover: hover) {
        --tw-grayscale: grayscale(0%);
        filter: var(--tw-blur,) var(--tw-brightness,) var(--tw-contrast,) var(--tw-grayscale,) var(--tw-hue-rotate,) var(--tw-invert,) var(--tw-saturate,) var(--tw-sepia,) var(--tw-drop-shadow,);
      }
    }
  }
  .hover\:before\:scale-105 {
    &:hover {
      @media (hover: hover) {
        &::before {
          content: var(--tw-content);
          --tw-scale-x: 105%;
          --tw-scale-y: 105%;
          --tw-scale-z: 105%;
          scale: var(--tw-scale-x) var(--tw-scale-y);
        }
      }
    }
  }
  .focus\:border-blue-300 {
    &:focus {
      border-color: var(--color-blue-300);
    }
  }
  .active\:scale-95 {
    &:active {
      --tw-scale-x: 95%;
      --tw-scale-y: 95%;
      --tw-scale-z: 95%;
      scale: var(--tw-scale-x) var(--tw-scale-y);
    }
  }
  .active\:cursor-grabbing {
    &:active {
      cursor: grabbing;
    }
  }
  .active\:text-gray-900 {
    &:active {
      color: var(--color-gray-900);
    }
  }
  .active\:duration-75 {
    &:active {
      --tw-duration: 75ms;
      transition-duration: 75ms;
    }
  }
  .active\:before\:scale-95 {
    &:active {
      &::before {
        content: var(--tw-content);
        --tw-scale-x: 95%;
        --tw-scale-y: 95%;
        --tw-scale-z: 95%;
        scale: var(--tw-scale-x) var(--tw-scale-y);
      }
    }
  }
  .sm\:mt-auto {
    @media (width >= 40rem) {
      margin-top: auto;
    }
  }
  .sm\:flex {
    @media (width >= 40rem) {
      display: flex;
    }
  }
  .sm\:w-5\/12 {
    @media (width >= 40rem) {
      width: calc(5/12 * 100%);
    }
  }
  .sm\:w-7\/12 {
    @media (width >= 40rem) {
      width: calc(7/12 * 100%);
    }
  }
  .sm\:w-max {
    @media (width >= 40rem) {
      width: max-content;
    }
  }
  .sm\:grid-cols-2 {
    @media (width >= 40rem) {
      grid-template-columns: repeat(2, minmax(0, 1fr));
    }
  }
  .sm\:grid-cols-4 {
    @media (width >= 40rem) {
      grid-template-columns: repeat(4, minmax(0, 1fr));
    }
  }
  .sm\:space-x-3 {
    @media (width >= 40rem) {
      :where(& > :not(:last-child)) {
        --tw-space-x-reverse: 0;
        margin-inline-start: calc(calc(var(--spacing) * 3) * var(--tw-space-x-reverse));
        margin-inline-end: calc(calc(var(--spacing) * 3) * calc(1 - var(--tw-space-x-reverse)));
      }
    }
  }
  .sm\:p-4 {
    @media (width >= 40rem) {
      padding: calc(var(--spacing) * 4);
    }
  }
  .sm\:p-8 {
    @media (width >= 40rem) {
      padding: calc(var(--spacing) * 8);
    }
  }
  .sm\:py-32 {
    @media (width >= 40rem) {
      padding-block: calc(var(--spacing) * 32);
    }
  }
  .sm\:text-left {
    @media (width >= 40rem) {
      text-align: left;
    }
  }
  .md\:mb-0 {
    @media (width >= 48rem) {
      margin-bottom: calc(var(--spacing) * 0);
    }
  }
  .md\:block {
    @media (width >= 48rem) {
      display: block;
    }
  }
  .md\:flex {
    @media (width >= 48rem) {
      display: flex;
    }
  }
  .md\:hidden {
    @media (width >= 48rem) {
      display: none;
    }
  }
  .md\:inline-block {
    @media (width >= 48rem) {
      display: inline-block;
    }
  }
  .md\:h-full {
    @media (width >= 48rem) {
      height: 100%;
    }
  }
  .md\:min-h-auto {
    @media (width >= 48rem) {
      min-height: auto;
    }
  }
  .md\:w-1\/2 {
    @media (width >= 48rem) {
      width: calc(1/2 * 100%);
    }
  }
  .md\:w-1\/3 {
    @media (width >= 48rem) {
      width: calc(1/3 * 100%);
    }
  }
  .md\:w-2\/3 {
    @media (width >= 48rem) {
      width: calc(2/3 * 100%);
    }
  }
  .md\:w-8\/12 {
    @media (width >= 48rem) {
      width: calc(8/12 * 100%);
    }
  }
  .md\:w-10\/12 {
    @media (width >= 48rem) {
      width: calc(10/12 * 100%);
    }
  }
  .md\:w-52 {
    @media (width >= 48rem) {
      width: calc(var(--spacing) * 52);
    }
  }
  .md\:flex-auto {
    @media (width >= 48rem) {
      flex: auto;
    }
  }
  .md\:grow {
    @media (width >= 48rem) {
      flex-grow: 1;
    }
  }
  .md\:columns-2 {
    @media (width >= 48rem) {
      columns: 2;
    }
  }
  .md\:grid-cols-2 {
    @media (width >= 48rem) {
      grid-template-columns: repeat(2, minmax(0, 1fr));
    }
  }
  .md\:grid-cols-6 {
    @media (width >= 48rem) {
      grid-template-columns: repeat(6, minmax(0, 1fr));
    }
  }
  .md\:flex-row {
    @media (width >= 48rem) {
      flex-direction: row;
    }
  }
  .md\:flex-nowrap {
    @media (width >= 48rem) {
      flex-wrap: nowrap;
    }
  }
  .md\:items-center {
    @media (width >= 48rem) {
      align-items: center;
    }
  }
  .md\:justify-between {
    @media (width >= 48rem) {
      justify-content: space-between;
    }
  }
  .md\:justify-start {
    @media (width >= 48rem) {
      justify-content: flex-start;
    }
  }
  .md\:gap-0 {
    @media (width >= 48rem) {
      gap: calc(var(--spacing) * 0);
    }
  }
  .md\:gap-6 {
    @media (width >= 48rem) {
      gap: calc(var(--spacing) * 6);
    }
  }
  .md\:space-y-0 {
    @media (width >= 48rem) {
      :where(& > :not(:last-child)) {
        --tw-space-y-reverse: 0;
        margin-block-start: calc(calc(var(--spacing) * 0) * var(--tw-space-y-reverse));
        margin-block-end: calc(calc(var(--spacing) * 0) * calc(1 - var(--tw-space-y-reverse)));
      }
    }
  }
  .md\:px-0 {
    @media (width >= 48rem) {
      padding-inline: calc(var(--spacing) * 0);
    }
  }
  .md\:px-4 {
    @media (width >= 48rem) {
      padding-inline: calc(var(--spacing) * 4);
    }
  }
  .md\:px-8 {
    @media (width >= 48rem) {
      padding-inline: calc(var(--spacing) * 8);
    }
  }
  .md\:px-12 {
    @media (width >= 48rem) {
      padding-inline: calc(var(--spacing) * 12);
    }
  }
  .md\:py-4 {
    @media (width >= 48rem) {
      padding-block: calc(var(--spacing) * 4);
    }
  }
  .md\:py-40 {
    @media (width >= 48rem) {
      padding-block: calc(var(--spacing) * 40);
    }
  }
  .md\:pt-px {
    @media (width >= 48rem) {
      padding-top: 1px;
    }
  }
  .md\:text-4xl {
    @media (width >= 48rem) {
      font-size: var(--text-4xl);
      line-height: var(--tw-leading, var(--text-4xl--line-height));
    }
  }
  .md\:text-5xl {
    @media (width >= 48rem) {
      font-size: var(--text-5xl);
      line-height: var(--tw-leading, var(--text-5xl--line-height));
    }
  }
  .lg\:visible {
    @media (width >= 64rem) {
      visibility: visible;
    }
  }
  .lg\:relative {
    @media (width >= 64rem) {
      position: relative;
    }
  }
  .lg\:col-span-2 {
    @media (width >= 64rem) {
      grid-column: span 2 / span 2;
    }
  }
  .lg\:mx-0 {
    @media (width >= 64rem) {
      margin-inline: calc(var(--spacing) * 0);
    }
  }
  .lg\:mx-auto {
    @media (width >= 64rem) {
      margin-inline: auto;
    }
  }
  .lg\:mt-0 {
    @media (width >= 64rem) {
      margin-top: calc(var(--spacing) * 0);
    }
  }
  .lg\:flex {
    @media (width >= 64rem) {
      display: flex;
    }
  }
  .lg\:hidden {
    @media (width >= 64rem) {
      display: none;
    }
  }
  .lg\:w-1\/2 {
    @media (width >= 64rem) {
      width: calc(1/2 * 100%);
    }
  }
  .lg\:w-2\/3 {
    @media (width >= 64rem) {
      width: calc(2/3 * 100%);
    }
  }
  .lg\:w-6\/12 {
    @media (width >= 64rem) {
      width: calc(6/12 * 100%);
    }
  }
  .lg\:w-7\/12 {
    @media (width >= 64rem) {
      width: calc(7/12 * 100%);
    }
  }
  .lg\:w-8\/12 {
    @media (width >= 64rem) {
      width: calc(8/12 * 100%);
    }
  }
  .lg\:w-auto {
    @media (width >= 64rem) {
      width: auto;
    }
  }
  .lg\:w-fit {
    @media (width >= 64rem) {
      width: fit-content;
    }
  }
  .lg\:max-w-none {
    @media (width >= 64rem) {
      max-width: none;
    }
  }
  .lg\:translate-y-0 {
    @media (width >= 64rem) {
      --tw-translate-y: calc(var(--spacing) * 0);
      translate: var(--tw-translate-x) var(--tw-translate-y);
    }
  }
  .lg\:scale-100 {
    @media (width >= 64rem) {
      --tw-scale-x: 100%;
      --tw-scale-y: 100%;
      --tw-scale-z: 100%;
      scale: var(--tw-scale-x) var(--tw-scale-y);
    }
  }
  .lg\:columns-3 {
    @media (width >= 64rem) {
      columns: 3;
    }
  }
  .lg\:grid-cols-3 {
    @media (width >= 64rem) {
      grid-template-columns: repeat(3, minmax(0, 1fr));
    }
  }
  .lg\:grid-cols-4 {
    @media (width >= 64rem) {
      grid-template-columns: repeat(4, minmax(0, 1fr));
    }
  }
  .lg\:flex-row {
    @media (width >= 64rem) {
      flex-direction: row;
    }
  }
  .lg\:items-center {
    @media (width >= 64rem) {
      align-items: center;
    }
  }
  .lg\:gap-0 {
    @media (width >= 64rem) {
      gap: calc(var(--spacing) * 0);
    }
  }
  .lg\:gap-12 {
    @media (width >= 64rem) {
      gap: calc(var(--spacing) * 12);
    }
  }
  .lg\:divide-y-0 {
    @media (width >= 64rem) {
      :where(& > :not(:last-child)) {
        --tw-divide-y-reverse: 0;
        border-bottom-style: var(--tw-border-style);
        border-top-style: var(--tw-border-style);
        border-top-width: calc(0px * var(--tw-divide-y-reverse));
        border-bottom-width: calc(0px * calc(1 - var(--tw-divide-y-reverse)));
      }
    }
  }
  .lg\:border-none {
    @media (width >= 64rem) {
      --tw-border-style: none;
      border-style: none;
    }
  }
  .lg\:border-transparent {
    @media (width >= 64rem) {
      border-color: transparent;
    }
  }
  .lg\:bg-transparent {
    @media (width >= 64rem) {
      background-color: transparent;
    }
  }
  .lg\:p-0 {
    @media (width >= 64rem) {
      padding: calc(var(--spacing) * 0);
    }
  }
  .lg\:px-8 {
    @media (width >= 64rem) {
      padding-inline: calc(var(--spacing) * 8);
    }
  }
  .lg\:px-20 {
    @media (width >= 64rem) {
      padding-inline: calc(var(--spacing) * 20);
    }
  }
  .lg\:py-10 {
    @media (width >= 64rem) {
      padding-block: calc(var(--spacing) * 10);
    }
  }
  .lg\:pt-0 {
    @media (width >= 64rem) {
      padding-top: calc(var(--spacing) * 0);
    }
  }
  .lg\:pr-4 {
    @media (width >= 64rem) {
      padding-right: calc(var(--spacing) * 4);
    }
  }
  .lg\:text-sm {
    @media (width >= 64rem) {
      font-size: var(--text-sm);
      line-height: var(--tw-leading, var(--text-sm--line-height));
    }
  }
  .lg\:opacity-100 {
    @media (width >= 64rem) {
      opacity: 100%;
    }
  }
  .lg\:shadow-none {
    @media (width >= 64rem) {
      --tw-shadow: 0 0 #0000;
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }
  }
  .lg\:group-data-\[state\=active\]\:translate-y-0 {
    @media (width >= 64rem) {
      &:is(:where(.group)[data-state="active"] *) {
        --tw-translate-y: calc(var(--spacing) * 0);
        translate: var(--tw-translate-x) var(--tw-translate-y);
      }
    }
  }
  .xl\:col-span-1 {
    @media (width >= 80rem) {
      grid-column: span 1 / span 1;
    }
  }
  .xl\:grid {
    @media (width >= 80rem) {
      display: grid;
    }
  }
  .xl\:w-6\/12 {
    @media (width >= 80rem) {
      width: calc(6/12 * 100%);
    }
  }
  .xl\:grid-cols-3 {
    @media (width >= 80rem) {
      grid-template-columns: repeat(3, minmax(0, 1fr));
    }
  }
  .xl\:grid-cols-4 {
    @media (width >= 80rem) {
      grid-template-columns: repeat(4, minmax(0, 1fr));
    }
  }
  .xl\:space-y-0 {
    @media (width >= 80rem) {
      :where(& > :not(:last-child)) {
        --tw-space-y-reverse: 0;
        margin-block-start: calc(calc(var(--spacing) * 0) * var(--tw-space-y-reverse));
        margin-block-end: calc(calc(var(--spacing) * 0) * calc(1 - var(--tw-space-y-reverse)));
      }
    }
  }
  .\@xs\:inline {
    @container (width >= 20rem) {
      display: inline;
    }
  }
  .\@sm\:-mb-5 {
    @container (width >= 24rem) {
      margin-bottom: calc(var(--spacing) * -5);
    }
  }
  .\@sm\:block {
    @container (width >= 24rem) {
      display: block;
    }
  }
  .\@sm\:hidden {
    @container (width >= 24rem) {
      display: none;
    }
  }
  .\@sm\:flex-row {
    @container (width >= 24rem) {
      flex-direction: row;
    }
  }
  .\@md\:mr-6 {
    @container (width >= 28rem) {
      margin-right: calc(var(--spacing) * 6);
    }
  }
  .\@md\/toolbar\:inline-block {
    @container toolbar (width >= 28rem) {
      display: inline-block;
    }
  }
  .\@md\:block {
    @container (width >= 28rem) {
      display: block;
    }
  }
  .\@md\:hidden {
    @container (width >= 28rem) {
      display: none;
    }
  }
  .\@md\:h-8 {
    @container (width >= 28rem) {
      height: calc(var(--spacing) * 8);
    }
  }
  .\@md\:w-8 {
    @container (width >= 28rem) {
      width: calc(var(--spacing) * 8);
    }
  }
  .\@md\:flex-row {
    @container (width >= 28rem) {
      flex-direction: row;
    }
  }
  .\@md\:items-center {
    @container (width >= 28rem) {
      align-items: center;
    }
  }
  .\@lg\:-mb-5 {
    @container (width >= 32rem) {
      margin-bottom: calc(var(--spacing) * -5);
    }
  }
  .\@lg\:w-1\/2 {
    @container (width >= 32rem) {
      width: calc(1/2 * 100%);
    }
  }
  .\@lg\:w-1\/3 {
    @container (width >= 32rem) {
      width: calc(1/3 * 100%);
    }
  }
  .\@lg\:w-1\/4 {
    @container (width >= 32rem) {
      width: calc(1/4 * 100%);
    }
  }
  .\@lg\:w-2\/3 {
    @container (width >= 32rem) {
      width: calc(2/3 * 100%);
    }
  }
  .\@lg\:w-3\/4 {
    @container (width >= 32rem) {
      width: calc(3/4 * 100%);
    }
  }
  .\@3xl\/toolbar\:inline-block {
    @container toolbar (width >= 48rem) {
      display: inline-block;
    }
  }
  .ltr\:right-0 {
    &:where(:dir(ltr), [dir="ltr"], [dir="ltr"] *) {
      right: calc(var(--spacing) * 0);
    }
  }
  .ltr\:right-2 {
    &:where(:dir(ltr), [dir="ltr"], [dir="ltr"] *) {
      right: calc(var(--spacing) * 2);
    }
  }
  .ltr\:right-2\.5 {
    &:where(:dir(ltr), [dir="ltr"], [dir="ltr"] *) {
      right: calc(var(--spacing) * 2.5);
    }
  }
  .ltr\:right-10 {
    &:where(:dir(ltr), [dir="ltr"], [dir="ltr"] *) {
      right: calc(var(--spacing) * 10);
    }
  }
  .ltr\:left-0 {
    &:where(:dir(ltr), [dir="ltr"], [dir="ltr"] *) {
      left: calc(var(--spacing) * 0);
    }
  }
  .ltr\:float-right {
    &:where(:dir(ltr), [dir="ltr"], [dir="ltr"] *) {
      float: right;
    }
  }
  .ltr\:mr-1 {
    &:where(:dir(ltr), [dir="ltr"], [dir="ltr"] *) {
      margin-right: calc(var(--spacing) * 1);
    }
  }
  .ltr\:mr-1\.5 {
    &:where(:dir(ltr), [dir="ltr"], [dir="ltr"] *) {
      margin-right: calc(var(--spacing) * 1.5);
    }
  }
  .ltr\:mr-2 {
    &:where(:dir(ltr), [dir="ltr"], [dir="ltr"] *) {
      margin-right: calc(var(--spacing) * 2);
    }
  }
  .ltr\:mr-3 {
    &:where(:dir(ltr), [dir="ltr"], [dir="ltr"] *) {
      margin-right: calc(var(--spacing) * 3);
    }
  }
  .ltr\:mr-4 {
    &:where(:dir(ltr), [dir="ltr"], [dir="ltr"] *) {
      margin-right: calc(var(--spacing) * 4);
    }
  }
  .ltr\:mr-6 {
    &:where(:dir(ltr), [dir="ltr"], [dir="ltr"] *) {
      margin-right: calc(var(--spacing) * 6);
    }
  }
  .ltr\:mr-8 {
    &:where(:dir(ltr), [dir="ltr"], [dir="ltr"] *) {
      margin-right: calc(var(--spacing) * 8);
    }
  }
  .ltr\:ml-0 {
    &:where(:dir(ltr), [dir="ltr"], [dir="ltr"] *) {
      margin-left: calc(var(--spacing) * 0);
    }
  }
  .ltr\:ml-1 {
    &:where(:dir(ltr), [dir="ltr"], [dir="ltr"] *) {
      margin-left: calc(var(--spacing) * 1);
    }
  }
  .ltr\:ml-2 {
    &:where(:dir(ltr), [dir="ltr"], [dir="ltr"] *) {
      margin-left: calc(var(--spacing) * 2);
    }
  }
  .ltr\:ml-2\.5 {
    &:where(:dir(ltr), [dir="ltr"], [dir="ltr"] *) {
      margin-left: calc(var(--spacing) * 2.5);
    }
  }
  .ltr\:ml-3 {
    &:where(:dir(ltr), [dir="ltr"], [dir="ltr"] *) {
      margin-left: calc(var(--spacing) * 3);
    }
  }
  .ltr\:ml-4 {
    &:where(:dir(ltr), [dir="ltr"], [dir="ltr"] *) {
      margin-left: calc(var(--spacing) * 4);
    }
  }
  .ltr\:ml-6 {
    &:where(:dir(ltr), [dir="ltr"], [dir="ltr"] *) {
      margin-left: calc(var(--spacing) * 6);
    }
  }
  .ltr\:rounded-l {
    &:where(:dir(ltr), [dir="ltr"], [dir="ltr"] *) {
      border-top-left-radius: 0.25rem;
      border-bottom-left-radius: 0.25rem;
    }
  }
  .ltr\:rounded-l-none {
    &:where(:dir(ltr), [dir="ltr"], [dir="ltr"] *) {
      border-top-left-radius: 0;
      border-bottom-left-radius: 0;
    }
  }
  .ltr\:rounded-r {
    &:where(:dir(ltr), [dir="ltr"], [dir="ltr"] *) {
      border-top-right-radius: 0.25rem;
      border-bottom-right-radius: 0.25rem;
    }
  }
  .ltr\:rounded-br {
    &:where(:dir(ltr), [dir="ltr"], [dir="ltr"] *) {
      border-bottom-right-radius: 0.25rem;
    }
  }
  .ltr\:rounded-bl {
    &:where(:dir(ltr), [dir="ltr"], [dir="ltr"] *) {
      border-bottom-left-radius: 0.25rem;
    }
  }
  .ltr\:border-r {
    &:where(:dir(ltr), [dir="ltr"], [dir="ltr"] *) {
      border-right-style: var(--tw-border-style);
      border-right-width: 1px;
    }
  }
  .ltr\:border-l {
    &:where(:dir(ltr), [dir="ltr"], [dir="ltr"] *) {
      border-left-style: var(--tw-border-style);
      border-left-width: 1px;
    }
  }
  .ltr\:border-l-0 {
    &:where(:dir(ltr), [dir="ltr"], [dir="ltr"] *) {
      border-left-style: var(--tw-border-style);
      border-left-width: 0px;
    }
  }
  .ltr\:pr-0 {
    &:where(:dir(ltr), [dir="ltr"], [dir="ltr"] *) {
      padding-right: calc(var(--spacing) * 0);
    }
  }
  .ltr\:pr-2 {
    &:where(:dir(ltr), [dir="ltr"], [dir="ltr"] *) {
      padding-right: calc(var(--spacing) * 2);
    }
  }
  .ltr\:pr-3 {
    &:where(:dir(ltr), [dir="ltr"], [dir="ltr"] *) {
      padding-right: calc(var(--spacing) * 3);
    }
  }
  .ltr\:pr-4 {
    &:where(:dir(ltr), [dir="ltr"], [dir="ltr"] *) {
      padding-right: calc(var(--spacing) * 4);
    }
  }
  .ltr\:pr-6 {
    &:where(:dir(ltr), [dir="ltr"], [dir="ltr"] *) {
      padding-right: calc(var(--spacing) * 6);
    }
  }
  .ltr\:pr-8 {
    &:where(:dir(ltr), [dir="ltr"], [dir="ltr"] *) {
      padding-right: calc(var(--spacing) * 8);
    }
  }
  .ltr\:pl-0 {
    &:where(:dir(ltr), [dir="ltr"], [dir="ltr"] *) {
      padding-left: calc(var(--spacing) * 0);
    }
  }
  .ltr\:pl-2 {
    &:where(:dir(ltr), [dir="ltr"], [dir="ltr"] *) {
      padding-left: calc(var(--spacing) * 2);
    }
  }
  .ltr\:pl-3 {
    &:where(:dir(ltr), [dir="ltr"], [dir="ltr"] *) {
      padding-left: calc(var(--spacing) * 3);
    }
  }
  .ltr\:pl-4 {
    &:where(:dir(ltr), [dir="ltr"], [dir="ltr"] *) {
      padding-left: calc(var(--spacing) * 4);
    }
  }
  .ltr\:pl-6 {
    &:where(:dir(ltr), [dir="ltr"], [dir="ltr"] *) {
      padding-left: calc(var(--spacing) * 6);
    }
  }
  .ltr\:text-left {
    &:where(:dir(ltr), [dir="ltr"], [dir="ltr"] *) {
      text-align: left;
    }
  }
  .ltr\:text-right {
    &:where(:dir(ltr), [dir="ltr"], [dir="ltr"] *) {
      text-align: right;
    }
  }
  .ltr\:md\:mr-6 {
    &:where(:dir(ltr), [dir="ltr"], [dir="ltr"] *) {
      @media (width >= 48rem) {
        margin-right: calc(var(--spacing) * 6);
      }
    }
  }
  .ltr\:md\:ml-4 {
    &:where(:dir(ltr), [dir="ltr"], [dir="ltr"] *) {
      @media (width >= 48rem) {
        margin-left: calc(var(--spacing) * 4);
      }
    }
  }
  .ltr\:md\:rounded-tr-md {
    &:where(:dir(ltr), [dir="ltr"], [dir="ltr"] *) {
      @media (width >= 48rem) {
        border-top-right-radius: var(--radius-md);
      }
    }
  }
  .rtl\:right-0 {
    &:where(:dir(rtl), [dir="rtl"], [dir="rtl"] *) {
      right: calc(var(--spacing) * 0);
    }
  }
  .rtl\:left-0 {
    &:where(:dir(rtl), [dir="rtl"], [dir="rtl"] *) {
      left: calc(var(--spacing) * 0);
    }
  }
  .rtl\:left-2 {
    &:where(:dir(rtl), [dir="rtl"], [dir="rtl"] *) {
      left: calc(var(--spacing) * 2);
    }
  }
  .rtl\:left-2\.5 {
    &:where(:dir(rtl), [dir="rtl"], [dir="rtl"] *) {
      left: calc(var(--spacing) * 2.5);
    }
  }
  .rtl\:left-10 {
    &:where(:dir(rtl), [dir="rtl"], [dir="rtl"] *) {
      left: calc(var(--spacing) * 10);
    }
  }
  .rtl\:float-left {
    &:where(:dir(rtl), [dir="rtl"], [dir="rtl"] *) {
      float: left;
    }
  }
  .rtl\:mr-0 {
    &:where(:dir(rtl), [dir="rtl"], [dir="rtl"] *) {
      margin-right: calc(var(--spacing) * 0);
    }
  }
  .rtl\:mr-1 {
    &:where(:dir(rtl), [dir="rtl"], [dir="rtl"] *) {
      margin-right: calc(var(--spacing) * 1);
    }
  }
  .rtl\:mr-2 {
    &:where(:dir(rtl), [dir="rtl"], [dir="rtl"] *) {
      margin-right: calc(var(--spacing) * 2);
    }
  }
  .rtl\:mr-2\.5 {
    &:where(:dir(rtl), [dir="rtl"], [dir="rtl"] *) {
      margin-right: calc(var(--spacing) * 2.5);
    }
  }
  .rtl\:mr-3 {
    &:where(:dir(rtl), [dir="rtl"], [dir="rtl"] *) {
      margin-right: calc(var(--spacing) * 3);
    }
  }
  .rtl\:mr-4 {
    &:where(:dir(rtl), [dir="rtl"], [dir="rtl"] *) {
      margin-right: calc(var(--spacing) * 4);
    }
  }
  .rtl\:mr-6 {
    &:where(:dir(rtl), [dir="rtl"], [dir="rtl"] *) {
      margin-right: calc(var(--spacing) * 6);
    }
  }
  .rtl\:ml-1 {
    &:where(:dir(rtl), [dir="rtl"], [dir="rtl"] *) {
      margin-left: calc(var(--spacing) * 1);
    }
  }
  .rtl\:ml-1\.5 {
    &:where(:dir(rtl), [dir="rtl"], [dir="rtl"] *) {
      margin-left: calc(var(--spacing) * 1.5);
    }
  }
  .rtl\:ml-2 {
    &:where(:dir(rtl), [dir="rtl"], [dir="rtl"] *) {
      margin-left: calc(var(--spacing) * 2);
    }
  }
  .rtl\:ml-3 {
    &:where(:dir(rtl), [dir="rtl"], [dir="rtl"] *) {
      margin-left: calc(var(--spacing) * 3);
    }
  }
  .rtl\:ml-4 {
    &:where(:dir(rtl), [dir="rtl"], [dir="rtl"] *) {
      margin-left: calc(var(--spacing) * 4);
    }
  }
  .rtl\:ml-6 {
    &:where(:dir(rtl), [dir="rtl"], [dir="rtl"] *) {
      margin-left: calc(var(--spacing) * 6);
    }
  }
  .rtl\:ml-8 {
    &:where(:dir(rtl), [dir="rtl"], [dir="rtl"] *) {
      margin-left: calc(var(--spacing) * 8);
    }
  }
  .rtl\:rotate-180 {
    &:where(:dir(rtl), [dir="rtl"], [dir="rtl"] *) {
      rotate: 180deg;
    }
  }
  .rtl\:space-x-reverse {
    &:where(:dir(rtl), [dir="rtl"], [dir="rtl"] *) {
      :where(& > :not(:last-child)) {
        --tw-space-x-reverse: 1;
      }
    }
  }
  .rtl\:rounded-l {
    &:where(:dir(rtl), [dir="rtl"], [dir="rtl"] *) {
      border-top-left-radius: 0.25rem;
      border-bottom-left-radius: 0.25rem;
    }
  }
  .rtl\:rounded-r {
    &:where(:dir(rtl), [dir="rtl"], [dir="rtl"] *) {
      border-top-right-radius: 0.25rem;
      border-bottom-right-radius: 0.25rem;
    }
  }
  .rtl\:rounded-r-none {
    &:where(:dir(rtl), [dir="rtl"], [dir="rtl"] *) {
      border-top-right-radius: 0;
      border-bottom-right-radius: 0;
    }
  }
  .rtl\:rounded-br {
    &:where(:dir(rtl), [dir="rtl"], [dir="rtl"] *) {
      border-bottom-right-radius: 0.25rem;
    }
  }
  .rtl\:rounded-bl {
    &:where(:dir(rtl), [dir="rtl"], [dir="rtl"] *) {
      border-bottom-left-radius: 0.25rem;
    }
  }
  .rtl\:border-r {
    &:where(:dir(rtl), [dir="rtl"], [dir="rtl"] *) {
      border-right-style: var(--tw-border-style);
      border-right-width: 1px;
    }
  }
  .rtl\:border-r-0 {
    &:where(:dir(rtl), [dir="rtl"], [dir="rtl"] *) {
      border-right-style: var(--tw-border-style);
      border-right-width: 0px;
    }
  }
  .rtl\:border-l {
    &:where(:dir(rtl), [dir="rtl"], [dir="rtl"] *) {
      border-left-style: var(--tw-border-style);
      border-left-width: 1px;
    }
  }
  .rtl\:pr-0 {
    &:where(:dir(rtl), [dir="rtl"], [dir="rtl"] *) {
      padding-right: calc(var(--spacing) * 0);
    }
  }
  .rtl\:pr-2 {
    &:where(:dir(rtl), [dir="rtl"], [dir="rtl"] *) {
      padding-right: calc(var(--spacing) * 2);
    }
  }
  .rtl\:pr-3 {
    &:where(:dir(rtl), [dir="rtl"], [dir="rtl"] *) {
      padding-right: calc(var(--spacing) * 3);
    }
  }
  .rtl\:pr-4 {
    &:where(:dir(rtl), [dir="rtl"], [dir="rtl"] *) {
      padding-right: calc(var(--spacing) * 4);
    }
  }
  .rtl\:pr-6 {
    &:where(:dir(rtl), [dir="rtl"], [dir="rtl"] *) {
      padding-right: calc(var(--spacing) * 6);
    }
  }
  .rtl\:pl-0 {
    &:where(:dir(rtl), [dir="rtl"], [dir="rtl"] *) {
      padding-left: calc(var(--spacing) * 0);
    }
  }
  .rtl\:pl-2 {
    &:where(:dir(rtl), [dir="rtl"], [dir="rtl"] *) {
      padding-left: calc(var(--spacing) * 2);
    }
  }
  .rtl\:pl-3 {
    &:where(:dir(rtl), [dir="rtl"], [dir="rtl"] *) {
      padding-left: calc(var(--spacing) * 3);
    }
  }
  .rtl\:pl-4 {
    &:where(:dir(rtl), [dir="rtl"], [dir="rtl"] *) {
      padding-left: calc(var(--spacing) * 4);
    }
  }
  .rtl\:pl-6 {
    &:where(:dir(rtl), [dir="rtl"], [dir="rtl"] *) {
      padding-left: calc(var(--spacing) * 6);
    }
  }
  .rtl\:pl-8 {
    &:where(:dir(rtl), [dir="rtl"], [dir="rtl"] *) {
      padding-left: calc(var(--spacing) * 8);
    }
  }
  .rtl\:text-left {
    &:where(:dir(rtl), [dir="rtl"], [dir="rtl"] *) {
      text-align: left;
    }
  }
  .rtl\:text-right {
    &:where(:dir(rtl), [dir="rtl"], [dir="rtl"] *) {
      text-align: right;
    }
  }
  .rtl\:md\:mr-4 {
    &:where(:dir(rtl), [dir="rtl"], [dir="rtl"] *) {
      @media (width >= 48rem) {
        margin-right: calc(var(--spacing) * 4);
      }
    }
  }
  .rtl\:md\:ml-6 {
    &:where(:dir(rtl), [dir="rtl"], [dir="rtl"] *) {
      @media (width >= 48rem) {
        margin-left: calc(var(--spacing) * 6);
      }
    }
  }
  .rtl\:md\:rounded-tl-md {
    &:where(:dir(rtl), [dir="rtl"], [dir="rtl"] *) {
      @media (width >= 48rem) {
        border-top-left-radius: var(--radius-md);
      }
    }
  }
  .dark\:divide-gray-700 {
    @media (prefers-color-scheme: dark) {
      :where(& > :not(:last-child)) {
        border-color: var(--color-gray-700);
      }
    }
  }
  .dark\:divide-gray-800 {
    @media (prefers-color-scheme: dark) {
      :where(& > :not(:last-child)) {
        border-color: var(--color-gray-800);
      }
    }
  }
  .dark\:border-none {
    @media (prefers-color-scheme: dark) {
      --tw-border-style: none;
      border-style: none;
    }
  }
  .dark\:border-gray-700 {
    @media (prefers-color-scheme: dark) {
      border-color: var(--color-gray-700);
    }
  }
  .dark\:border-gray-800 {
    @media (prefers-color-scheme: dark) {
      border-color: var(--color-gray-800);
    }
  }
  .dark\:border-gray-900 {
    @media (prefers-color-scheme: dark) {
      border-color: var(--color-gray-900);
    }
  }
  .dark\:border-white\/5 {
    @media (prefers-color-scheme: dark) {
      border-color: color-mix(in srgb, #fff 5%, transparent);
      @supports (color: color-mix(in lab, red, red)) {
        border-color: color-mix(in oklab, var(--color-white) 5%, transparent);
      }
    }
  }
  .dark\:bg-blue-900 {
    @media (prefers-color-scheme: dark) {
      background-color: var(--color-blue-900);
    }
  }
  .dark\:bg-gray-800 {
    @media (prefers-color-scheme: dark) {
      background-color: var(--color-gray-800);
    }
  }
  .dark\:bg-gray-900 {
    @media (prefers-color-scheme: dark) {
      background-color: var(--color-gray-900);
    }
  }
  .dark\:bg-gray-950 {
    @media (prefers-color-scheme: dark) {
      background-color: var(--color-gray-950);
    }
  }
  .dark\:bg-gray-950\/70 {
    @media (prefers-color-scheme: dark) {
      background-color: color-mix(in srgb, oklch(13% 0.028 261.692) 70%, transparent);
      @supports (color: color-mix(in lab, red, red)) {
        background-color: color-mix(in oklab, var(--color-gray-950) 70%, transparent);
      }
    }
  }
  .dark\:bg-indigo-900\/20 {
    @media (prefers-color-scheme: dark) {
      background-color: color-mix(in srgb, oklch(35.9% 0.144 278.697) 20%, transparent);
      @supports (color: color-mix(in lab, red, red)) {
        background-color: color-mix(in oklab, var(--color-indigo-900) 20%, transparent);
      }
    }
  }
  .dark\:bg-teal-900\/20 {
    @media (prefers-color-scheme: dark) {
      background-color: color-mix(in srgb, oklch(38.6% 0.063 188.416) 20%, transparent);
      @supports (color: color-mix(in lab, red, red)) {
        background-color: color-mix(in oklab, var(--color-teal-900) 20%, transparent);
      }
    }
  }
  .dark\:bg-white {
    @media (prefers-color-scheme: dark) {
      background-color: var(--color-white);
    }
  }
  .dark\:bg-white\/10 {
    @media (prefers-color-scheme: dark) {
      background-color: color-mix(in srgb, #fff 10%, transparent);
      @supports (color: color-mix(in lab, red, red)) {
        background-color: color-mix(in oklab, var(--color-white) 10%, transparent);
      }
    }
  }
  .dark\:from-blue-700 {
    @media (prefers-color-scheme: dark) {
      --tw-gradient-from: var(--color-blue-700);
      --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
    }
  }
  .dark\:to-indigo-600 {
    @media (prefers-color-scheme: dark) {
      --tw-gradient-to: var(--color-indigo-600);
      --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
    }
  }
  .dark\:text-blue-300 {
    @media (prefers-color-scheme: dark) {
      color: var(--color-blue-300);
    }
  }
  .dark\:text-blue-600 {
    @media (prefers-color-scheme: dark) {
      color: var(--color-blue-600);
    }
  }
  .dark\:text-gray-200 {
    @media (prefers-color-scheme: dark) {
      color: var(--color-gray-200);
    }
  }
  .dark\:text-gray-300 {
    @media (prefers-color-scheme: dark) {
      color: var(--color-gray-300);
    }
  }
  .dark\:text-gray-600 {
    @media (prefers-color-scheme: dark) {
      color: var(--color-gray-600);
    }
  }
  .dark\:text-indigo-400 {
    @media (prefers-color-scheme: dark) {
      color: var(--color-indigo-400);
    }
  }
  .dark\:text-orange-300 {
    @media (prefers-color-scheme: dark) {
      color: var(--color-orange-300);
    }
  }
  .dark\:text-teal-300 {
    @media (prefers-color-scheme: dark) {
      color: var(--color-teal-300);
    }
  }
  .dark\:text-teal-400 {
    @media (prefers-color-scheme: dark) {
      color: var(--color-teal-400);
    }
  }
  .dark\:text-white {
    @media (prefers-color-scheme: dark) {
      color: var(--color-white);
    }
  }
  .dark\:opacity-20 {
    @media (prefers-color-scheme: dark) {
      opacity: 20%;
    }
  }
  .dark\:shadow-none {
    @media (prefers-color-scheme: dark) {
      --tw-shadow: 0 0 #0000;
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }
  }
  .dark\:group-hover\:bg-gray-800 {
    @media (prefers-color-scheme: dark) {
      &:is(:where(.group):hover *) {
        @media (hover: hover) {
          background-color: var(--color-gray-800);
        }
      }
    }
  }
  .dark\:before\:border-gray-700 {
    @media (prefers-color-scheme: dark) {
      &::before {
        content: var(--tw-content);
        border-color: var(--color-gray-700);
      }
    }
  }
  .dark\:before\:bg-gray-800 {
    @media (prefers-color-scheme: dark) {
      &::before {
        content: var(--tw-content);
        background-color: var(--color-gray-800);
      }
    }
  }
  .dark\:hover\:text-gray-400 {
    @media (prefers-color-scheme: dark) {
      &:hover {
        @media (hover: hover) {
          color: var(--color-gray-400);
        }
      }
    }
  }
  .dark\:hover\:text-gray-500 {
    @media (prefers-color-scheme: dark) {
      &:hover {
        @media (hover: hover) {
          color: var(--color-gray-500);
        }
      }
    }
  }
  .dark\:hover\:text-white {
    @media (prefers-color-scheme: dark) {
      &:hover {
        @media (hover: hover) {
          color: var(--color-white);
        }
      }
    }
  }
  .lg\:dark\:bg-transparent {
    @media (width >= 64rem) {
      @media (prefers-color-scheme: dark) {
        background-color: transparent;
      }
    }
  }
  .\[\&\:\:-webkit-calendar-picker-indicator\]\:hidden {
    &::-webkit-calendar-picker-indicator {
      display: none;
    }
  }
}
@layer base {
  * {
    font-family: "Urbanist", serif;
    scroll-behavior: smooth;
  }
  h1 {
    font-size: var(--text-5xl);
    line-height: var(--tw-leading, var(--text-5xl--line-height));
    --tw-font-weight: var(--font-weight-bold);
    font-weight: var(--font-weight-bold);
    text-wrap: balance;
    color: var(--color-gray-900);
    @media (width >= 48rem) {
      font-size: var(--text-6xl);
      line-height: var(--tw-leading, var(--text-6xl--line-height));
    }
    @media (width >= 80rem) {
      font-size: var(--text-7xl);
      line-height: var(--tw-leading, var(--text-7xl--line-height));
    }
    @media (prefers-color-scheme: dark) {
      color: var(--color-white);
    }
  }
  h2 {
    font-size: var(--text-3xl);
    line-height: var(--tw-leading, var(--text-3xl--line-height));
    --tw-font-weight: var(--font-weight-bold);
    font-weight: var(--font-weight-bold);
    color: var(--color-gray-800);
    @media (width >= 48rem) {
      font-size: var(--text-4xl);
      line-height: var(--tw-leading, var(--text-4xl--line-height));
    }
    @media (prefers-color-scheme: dark) {
      color: var(--color-white);
    }
  }
  h3 {
    font-size: var(--text-lg);
    line-height: var(--tw-leading, var(--text-lg--line-height));
    --tw-font-weight: var(--font-weight-semibold);
    font-weight: var(--font-weight-semibold);
    color: var(--color-gray-700);
    @media (prefers-color-scheme: dark) {
      color: var(--color-primary);
    }
  }
  p, span {
    color: var(--color-gray-500);
    @media (prefers-color-scheme: dark) {
      color: var(--color-gray-400);
    }
  }
}
@property --tw-translate-x {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}
@property --tw-translate-y {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}
@property --tw-translate-z {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}
@property --tw-scale-x {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}
@property --tw-scale-y {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}
@property --tw-scale-z {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}
@property --tw-rotate-x {
  syntax: "*";
  inherits: false;
}
@property --tw-rotate-y {
  syntax: "*";
  inherits: false;
}
@property --tw-rotate-z {
  syntax: "*";
  inherits: false;
}
@property --tw-skew-x {
  syntax: "*";
  inherits: false;
}
@property --tw-skew-y {
  syntax: "*";
  inherits: false;
}
@property --tw-space-y-reverse {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}
@property --tw-space-x-reverse {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}
@property --tw-divide-x-reverse {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}
@property --tw-border-style {
  syntax: "*";
  inherits: false;
  initial-value: solid;
}
@property --tw-divide-y-reverse {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}
@property --tw-gradient-position {
  syntax: "*";
  inherits: false;
}
@property --tw-gradient-from {
  syntax: "<color>";
  inherits: false;
  initial-value: #0000;
}
@property --tw-gradient-via {
  syntax: "<color>";
  inherits: false;
  initial-value: #0000;
}
@property --tw-gradient-to {
  syntax: "<color>";
  inherits: false;
  initial-value: #0000;
}
@property --tw-gradient-stops {
  syntax: "*";
  inherits: false;
}
@property --tw-gradient-via-stops {
  syntax: "*";
  inherits: false;
}
@property --tw-gradient-from-position {
  syntax: "<length-percentage>";
  inherits: false;
  initial-value: 0%;
}
@property --tw-gradient-via-position {
  syntax: "<length-percentage>";
  inherits: false;
  initial-value: 50%;
}
@property --tw-gradient-to-position {
  syntax: "<length-percentage>";
  inherits: false;
  initial-value: 100%;
}
@property --tw-leading {
  syntax: "*";
  inherits: false;
}
@property --tw-font-weight {
  syntax: "*";
  inherits: false;
}
@property --tw-tracking {
  syntax: "*";
  inherits: false;
}
@property --tw-ordinal {
  syntax: "*";
  inherits: false;
}
@property --tw-slashed-zero {
  syntax: "*";
  inherits: false;
}
@property --tw-numeric-figure {
  syntax: "*";
  inherits: false;
}
@property --tw-numeric-spacing {
  syntax: "*";
  inherits: false;
}
@property --tw-numeric-fraction {
  syntax: "*";
  inherits: false;
}
@property --tw-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-shadow-color {
  syntax: "*";
  inherits: false;
}
@property --tw-shadow-alpha {
  syntax: "<percentage>";
  inherits: false;
  initial-value: 100%;
}
@property --tw-inset-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-inset-shadow-color {
  syntax: "*";
  inherits: false;
}
@property --tw-inset-shadow-alpha {
  syntax: "<percentage>";
  inherits: false;
  initial-value: 100%;
}
@property --tw-ring-color {
  syntax: "*";
  inherits: false;
}
@property --tw-ring-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-inset-ring-color {
  syntax: "*";
  inherits: false;
}
@property --tw-inset-ring-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-ring-inset {
  syntax: "*";
  inherits: false;
}
@property --tw-ring-offset-width {
  syntax: "<length>";
  inherits: false;
  initial-value: 0px;
}
@property --tw-ring-offset-color {
  syntax: "*";
  inherits: false;
  initial-value: #fff;
}
@property --tw-ring-offset-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-outline-style {
  syntax: "*";
  inherits: false;
  initial-value: solid;
}
@property --tw-blur {
  syntax: "*";
  inherits: false;
}
@property --tw-brightness {
  syntax: "*";
  inherits: false;
}
@property --tw-contrast {
  syntax: "*";
  inherits: false;
}
@property --tw-grayscale {
  syntax: "*";
  inherits: false;
}
@property --tw-hue-rotate {
  syntax: "*";
  inherits: false;
}
@property --tw-invert {
  syntax: "*";
  inherits: false;
}
@property --tw-opacity {
  syntax: "*";
  inherits: false;
}
@property --tw-saturate {
  syntax: "*";
  inherits: false;
}
@property --tw-sepia {
  syntax: "*";
  inherits: false;
}
@property --tw-drop-shadow {
  syntax: "*";
  inherits: false;
}
@property --tw-drop-shadow-color {
  syntax: "*";
  inherits: false;
}
@property --tw-drop-shadow-alpha {
  syntax: "<percentage>";
  inherits: false;
  initial-value: 100%;
}
@property --tw-drop-shadow-size {
  syntax: "*";
  inherits: false;
}
@property --tw-backdrop-blur {
  syntax: "*";
  inherits: false;
}
@property --tw-backdrop-brightness {
  syntax: "*";
  inherits: false;
}
@property --tw-backdrop-contrast {
  syntax: "*";
  inherits: false;
}
@property --tw-backdrop-grayscale {
  syntax: "*";
  inherits: false;
}
@property --tw-backdrop-hue-rotate {
  syntax: "*";
  inherits: false;
}
@property --tw-backdrop-invert {
  syntax: "*";
  inherits: false;
}
@property --tw-backdrop-opacity {
  syntax: "*";
  inherits: false;
}
@property --tw-backdrop-saturate {
  syntax: "*";
  inherits: false;
}
@property --tw-backdrop-sepia {
  syntax: "*";
  inherits: false;
}
@property --tw-duration {
  syntax: "*";
  inherits: false;
}
@property --tw-ease {
  syntax: "*";
  inherits: false;
}
@property --tw-content {
  syntax: "*";
  initial-value: "";
  inherits: false;
}
@layer properties {
  @supports ((-webkit-hyphens: none) and (not (margin-trim: inline))) or ((-moz-orient: inline) and (not (color:rgb(from red r g b)))) {
    *, ::before, ::after, ::backdrop {
      --tw-translate-x: 0;
      --tw-translate-y: 0;
      --tw-translate-z: 0;
      --tw-scale-x: 1;
      --tw-scale-y: 1;
      --tw-scale-z: 1;
      --tw-rotate-x: initial;
      --tw-rotate-y: initial;
      --tw-rotate-z: initial;
      --tw-skew-x: initial;
      --tw-skew-y: initial;
      --tw-space-y-reverse: 0;
      --tw-space-x-reverse: 0;
      --tw-divide-x-reverse: 0;
      --tw-border-style: solid;
      --tw-divide-y-reverse: 0;
      --tw-gradient-position: initial;
      --tw-gradient-from: #0000;
      --tw-gradient-via: #0000;
      --tw-gradient-to: #0000;
      --tw-gradient-stops: initial;
      --tw-gradient-via-stops: initial;
      --tw-gradient-from-position: 0%;
      --tw-gradient-via-position: 50%;
      --tw-gradient-to-position: 100%;
      --tw-leading: initial;
      --tw-font-weight: initial;
      --tw-tracking: initial;
      --tw-ordinal: initial;
      --tw-slashed-zero: initial;
      --tw-numeric-figure: initial;
      --tw-numeric-spacing: initial;
      --tw-numeric-fraction: initial;
      --tw-shadow: 0 0 #0000;
      --tw-shadow-color: initial;
      --tw-shadow-alpha: 100%;
      --tw-inset-shadow: 0 0 #0000;
      --tw-inset-shadow-color: initial;
      --tw-inset-shadow-alpha: 100%;
      --tw-ring-color: initial;
      --tw-ring-shadow: 0 0 #0000;
      --tw-inset-ring-color: initial;
      --tw-inset-ring-shadow: 0 0 #0000;
      --tw-ring-inset: initial;
      --tw-ring-offset-width: 0px;
      --tw-ring-offset-color: #fff;
      --tw-ring-offset-shadow: 0 0 #0000;
      --tw-outline-style: solid;
      --tw-blur: initial;
      --tw-brightness: initial;
      --tw-contrast: initial;
      --tw-grayscale: initial;
      --tw-hue-rotate: initial;
      --tw-invert: initial;
      --tw-opacity: initial;
      --tw-saturate: initial;
      --tw-sepia: initial;
      --tw-drop-shadow: initial;
      --tw-drop-shadow-color: initial;
      --tw-drop-shadow-alpha: 100%;
      --tw-drop-shadow-size: initial;
      --tw-backdrop-blur: initial;
      --tw-backdrop-brightness: initial;
      --tw-backdrop-contrast: initial;
      --tw-backdrop-grayscale: initial;
      --tw-backdrop-hue-rotate: initial;
      --tw-backdrop-invert: initial;
      --tw-backdrop-opacity: initial;
      --tw-backdrop-saturate: initial;
      --tw-backdrop-sepia: initial;
      --tw-duration: initial;
      --tw-ease: initial;
      --tw-content: "";
    }
  }
}
